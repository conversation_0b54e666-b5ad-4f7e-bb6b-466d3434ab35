"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/page",{

/***/ "(app-pages-browser)/./src/app/warranties/page.tsx":
/*!*************************************!*\
  !*** ./src/app/warranties/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WarrantiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileDown,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n/**\n * Warranty Management Main Page\n *\n * This page provides an overview of warranty management with quick access\n * to different warranty categories and status information.\n */ function WarrantiesPage() {\n    _s();\n    _s1();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        active: 0,\n        expiring: 0,\n        expired: 0,\n        pending: 0,\n        total: 0\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load warranty statistics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WarrantiesPage.useEffect\": ()=>{\n            const loadStats = {\n                \"WarrantiesPage.useEffect.loadStats\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const [warrantyRes, expiringRes] = await Promise.all([\n                            fetch('/api/warranties?take=1000', {\n                                credentials: 'include'\n                            }),\n                            fetch('/api/warranties/expiring?days=30', {\n                                credentials: 'include'\n                            })\n                        ]);\n                        if (!warrantyRes.ok || !expiringRes.ok) {\n                            throw new Error('Failed to fetch warranty statistics');\n                        }\n                        const [warrantyData, expiringData] = await Promise.all([\n                            warrantyRes.json(),\n                            expiringRes.json()\n                        ]);\n                        const warranties = warrantyData.warranties || [];\n                        const expiringWarranties = expiringData.warranties || [];\n                        const newStats = {\n                            total: warranties.length,\n                            active: warranties.filter({\n                                \"WarrantiesPage.useEffect.loadStats\": (w)=>w.status === 'ACTIVE'\n                            }[\"WarrantiesPage.useEffect.loadStats\"]).length,\n                            expired: warranties.filter({\n                                \"WarrantiesPage.useEffect.loadStats\": (w)=>w.status === 'EXPIRED'\n                            }[\"WarrantiesPage.useEffect.loadStats\"]).length,\n                            pending: warranties.filter({\n                                \"WarrantiesPage.useEffect.loadStats\": (w)=>w.status === 'PENDING'\n                            }[\"WarrantiesPage.useEffect.loadStats\"]).length,\n                            expiring: expiringWarranties.length\n                        };\n                        setStats(newStats);\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error loading warranty statistics:', err);\n                        setError('Failed to load warranty statistics');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"WarrantiesPage.useEffect.loadStats\"];\n            loadStats();\n        }\n    }[\"WarrantiesPage.useEffect\"], []);\n    const handleTabChange = (value)=>{\n        setActiveTab(value);\n    };\n    const handleExport = async ()=>{\n        try {\n            const response = await fetch('/api/warranties/export?format=CSV', {\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export warranties');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"all-warranties-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Error exporting warranties:', error);\n            setError('Failed to export warranties');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Manage in-warranty and out-of-warranty products, track components, and monitor warranty status\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: handleExport,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"secondary\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/warranties/status\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Status Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                        defaultValue: \"overview\",\n                        value: activeTab,\n                        onValueChange: handleTabChange,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"overview\",\n                                        children: \"Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"in-warranty\",\n                                        children: \"In-Warranty\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"out-warranty\",\n                                        children: \"Out-of-Warranty\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"components\",\n                                        children: \"Components\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                        value: \"alerts\",\n                                        children: \"Alerts\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"overview\",\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Active Warranties\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Expiring Soon\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Expired\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        className: \"pb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Pending\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl font-bold text-black\",\n                                                                    children: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"In-Warranty Management\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 184,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Manage products currently under warranty coverage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/in-warranty\",\n                                                                children: \"View In-Warranty Products\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Out-of-Warranty Management\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Manage products that are no longer under warranty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/out-warranty\",\n                                                                children: \"View Out-of-Warranty Products\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Component Tracking\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Track individual components and their warranty status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/components\",\n                                                                children: \"View Component Tracking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Warranty Status Dashboard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Monitor warranty status and expiration alerts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/status\",\n                                                                children: \"View Status Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"BLUESTAR Warranties\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"Manage vendor-specific BLUESTAR warranty workflows\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/bluestar\",\n                                                                children: \"View BLUESTAR Warranties\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                className: \"hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"flex items-center space-x-2 text-black\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Warranty Alerts\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                className: \"text-black\",\n                                                                children: \"View and manage warranty expiration notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            asChild: true,\n                                                            className: \"w-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/warranties/alerts\",\n                                                                children: \"View Warranty Alerts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"in-warranty\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"In-Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display in-warranty products and management tools.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/in-warranty\",\n                                                children: \"Go to In-Warranty Management\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"out-warranty\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"Out-of-Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display out-of-warranty products and management tools.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/out-warranty\",\n                                                children: \"Go to Out-of-Warranty Management\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"components\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"Component Tracking\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display component warranty tracking tools.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/components\",\n                                                children: \"Go to Component Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"alerts\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileDown_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"Warranty Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"This section will display warranty expiration alerts and notifications.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                href: \"/warranties/alerts\",\n                                                children: \"Go to Warranty Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 10\n    }, this);\n}\n_s(WarrantiesPage, \"LCb8EBKFfo12nkIfeoi0ldqfTao=\");\n_c1 = WarrantiesPage;\n_s1(WarrantiesPage, \"LCb8EBKFfo12nkIfeoi0ldqfTao=\");\n_c = WarrantiesPage;\nvar _c;\n$RefreshReg$(_c, \"WarrantiesPage\");\nvar _c1;\n$RefreshReg$(_c1, \"WarrantiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/page.tsx\n"));

/***/ })

});