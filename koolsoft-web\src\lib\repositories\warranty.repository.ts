import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Warranty Repository
 *
 * This repository handles database operations for the Warranty entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class WarrantyRepository extends PrismaRepository<
  Prisma.warrantiesGetPayload<{}>,
  string,
  Prisma.warrantiesCreateInput,
  Prisma.warrantiesUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('warranties');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find warranties by customer ID
   * @param customerId Customer ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranties
   */
  async findByCustomerId(customerId: string, skip?: number, take?: number): Promise<Prisma.warrantiesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { customerId },
      skip,
      take,
      orderBy: { installDate: 'desc' },
    });
  }

  /**
   * Find active warranties (warranty date in the future)
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of active warranties
   */
  async findActive(skip?: number, take?: number): Promise<Prisma.warrantiesGetPayload<{}>[]> {
    return this.model.findMany({
      where: {
        warrantyDate: {
          gte: new Date(),
        },
        status: 'ACTIVE',
      },
      skip,
      take,
      orderBy: { warrantyDate: 'asc' },
    });
  }

  /**
   * Find expiring warranties (warranty date within the next X days)
   * @param days Number of days to look ahead
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of expiring warranties
   */
  async findExpiring(days: number = 30, skip?: number, take?: number): Promise<Prisma.warrantiesGetPayload<{}>[]> {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + days);

    return this.model.findMany({
      where: {
        warrantyDate: {
          gte: today,
          lte: futureDate,
        },
        status: 'ACTIVE',
      },
      skip,
      take,
      orderBy: { warrantyDate: 'asc' },
    });
  }

  /**
   * Find warranties by status
   * @param status Warranty status
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranties
   */
  async findByStatus(status: string, skip?: number, take?: number): Promise<Prisma.warrantiesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { status },
      skip,
      take,
      orderBy: { installDate: 'desc' },
    });
  }

  /**
   * Find warranty by original ID
   * @param originalId Original ID from legacy table
   * @returns Promise resolving to the warranty or null if not found
   */
  async findByOriginalId(originalId: number): Promise<Prisma.warrantiesGetPayload<{}> | null> {
    return this.model.findFirst({
      where: { originalId },
    });
  }

  /**
   * Find warranty with all related data
   * @param id Warranty ID
   * @returns Promise resolving to the warranty with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        customer: true,
        contactPerson: true,
        machines: {
          include: {
            product: true,
            model: true,
            brand: true,
            components: true,
          },
        },
      },
    });
  }

  /**
   * Find warranties by executive ID
   * @param executiveId Executive ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranties
   */
  async findByExecutiveId(executiveId: string, skip?: number, take?: number): Promise<Prisma.warrantiesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { executiveId },
      skip,
      take,
      orderBy: { installDate: 'desc' },
    });
  }

  /**
   * Find warranties with filter, pagination, and sorting
   * @param filter Filter condition
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of warranties
   */
  async findWithFilter(
    filter: any,
    skip?: number,
    take?: number,
    orderBy?: any
  ): Promise<Prisma.warrantiesGetPayload<{}>[]> {
    console.log('WarrantyRepository.findWithFilter: Starting with params:', {
      filter,
      skip,
      take,
      orderBy
    });

    try {
      console.log('WarrantyRepository.findWithFilter: Checking model availability');
      if (!this.model) {
        console.error('WarrantyRepository.findWithFilter: Model is not available');
        return [];
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);

      console.log('WarrantyRepository.findWithFilter: Executing findMany query');
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { installDate: 'desc' },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
              phone: true,
            },
          },
          contactPerson: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
      });

      console.log('WarrantyRepository.findWithFilter: Query completed successfully, found', result.length, 'records');
      return result;
    } catch (error) {
      console.error('WarrantyRepository.findWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Count warranties with filter
   * @param filter Filter condition
   * @returns Promise resolving to the count
   */
  async countWithFilter(filter: any): Promise<number> {
    console.log('WarrantyRepository.countWithFilter: Starting with filter:', filter);

    try {
      if (!this.model) {
        console.error('WarrantyRepository.countWithFilter: Model is not available');
        return 0;
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);

      const count = await this.model.count({
        where: filter,
      });

      console.log('WarrantyRepository.countWithFilter: Count completed successfully, found', count, 'records');
      return count;
    } catch (error) {
      console.error('WarrantyRepository.countWithFilter: Error executing count:', error);
      throw error;
    }
  }

  /**
   * Validate filter to ensure it only contains valid fields
   * @param filter Filter condition
   */
  private validateFilter(filter: any): void {
    if (!filter || typeof filter !== 'object') {
      return;
    }

    const validFields = [
      'id', 'customerId', 'executiveId', 'contactPersonId', 'bslNo', 'bslDate',
      'bslAmount', 'frequency', 'numberOfMachines', 'installDate', 'warrantyDate',
      'warningDate', 'technicianId', 'amcId', 'status', 'originalId', 'createdAt', 'updatedAt'
    ];

    for (const key in filter) {
      if (!validFields.includes(key) && !key.includes('.')) {
        console.warn(`WarrantyRepository.validateFilter: Invalid filter field '${key}' removed`);
        delete filter[key];
      }
    }
  }

  /**
   * Create warranty with related data in a transaction
   * @param data Warranty data with related entities
   * @returns Promise resolving to the created warranty
   */
  async createWithRelations(data: any): Promise<any> {
    const {
      machines,
      ...warrantyData
    } = data;

    return this.prisma.$transaction(async (tx) => {
      // Create the warranty
      const warranty = await tx.warranties.create({
        data: warrantyData,
      });

      // Create machines if provided
      if (machines && machines.length > 0) {
        await tx.warranty_machines.createMany({
          data: machines.map((machine: any) => ({
            ...machine,
            warrantyId: warranty.id,
          })),
        });
      }

      // Return the warranty with related data
      return tx.warranties.findUnique({
        where: { id: warranty.id },
        include: {
          customer: true,
          contactPerson: true,
          machines: {
            include: {
              product: true,
              model: true,
              brand: true,
              components: true,
            },
          },
        },
      });
    });
  }

  /**
   * Update warranty status based on warranty dates
   * @returns Promise resolving to the number of updated warranties
   */
  async updateExpiredStatuses(): Promise<number> {
    const today = new Date();
    
    const result = await this.model.updateMany({
      where: {
        warrantyDate: {
          lt: today,
        },
        status: 'ACTIVE',
      },
      data: {
        status: 'EXPIRED',
      },
    });

    return result.count;
  }
}
