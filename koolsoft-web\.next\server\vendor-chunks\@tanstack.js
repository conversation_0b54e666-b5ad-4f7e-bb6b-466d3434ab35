"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = onFocus => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(focused => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach(listener => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ??\n    // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({\n        type: \"continue\"\n      });\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({\n          type: \"pending\",\n          variables,\n          isPaused\n        });\n        await this.#mutationCache.config.onMutate?.(variables, this);\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(data, variables, this.state.context, this);\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(data, null, this.state.variables, this.state.context, this);\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({\n        type: \"success\",\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(error, variables, this.state.context, this);\n        await this.options.onError?.(error, variables, this.state.context);\n        await this.#mutationCache.config.onSettled?.(void 0, error, this.state.variables, this.state.context, this);\n        await this.options.onSettled?.(void 0, error, variables, this.state.context);\n        throw error;\n      } finally {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL211dGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDbUQ7QUFDUjtBQUNFO0FBQzdDLElBQUlHLFFBQVEsR0FBRyxjQUFjRixvREFBUyxDQUFDO0VBQ3JDLENBQUNHLFNBQVM7RUFDVixDQUFDQyxhQUFhO0VBQ2QsQ0FBQ0MsT0FBTztFQUNSQyxXQUFXQSxDQUFDQyxNQUFNLEVBQUU7SUFDbEIsS0FBSyxDQUFDLENBQUM7SUFDUCxJQUFJLENBQUNDLFVBQVUsR0FBR0QsTUFBTSxDQUFDQyxVQUFVO0lBQ25DLElBQUksQ0FBQyxDQUFDSixhQUFhLEdBQUdHLE1BQU0sQ0FBQ0gsYUFBYTtJQUMxQyxJQUFJLENBQUMsQ0FBQ0QsU0FBUyxHQUFHLEVBQUU7SUFDcEIsSUFBSSxDQUFDTSxLQUFLLEdBQUdGLE1BQU0sQ0FBQ0UsS0FBSyxJQUFJQyxlQUFlLENBQUMsQ0FBQztJQUM5QyxJQUFJLENBQUNDLFVBQVUsQ0FBQ0osTUFBTSxDQUFDSyxPQUFPLENBQUM7SUFDL0IsSUFBSSxDQUFDQyxVQUFVLENBQUMsQ0FBQztFQUNuQjtFQUNBRixVQUFVQSxDQUFDQyxPQUFPLEVBQUU7SUFDbEIsSUFBSSxDQUFDQSxPQUFPLEdBQUdBLE9BQU87SUFDdEIsSUFBSSxDQUFDRSxZQUFZLENBQUMsSUFBSSxDQUFDRixPQUFPLENBQUNHLE1BQU0sQ0FBQztFQUN4QztFQUNBLElBQUlDLElBQUlBLENBQUEsRUFBRztJQUNULE9BQU8sSUFBSSxDQUFDSixPQUFPLENBQUNJLElBQUk7RUFDMUI7RUFDQUMsV0FBV0EsQ0FBQ0MsUUFBUSxFQUFFO0lBQ3BCLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQ2YsU0FBUyxDQUFDZ0IsUUFBUSxDQUFDRCxRQUFRLENBQUMsRUFBRTtNQUN2QyxJQUFJLENBQUMsQ0FBQ2YsU0FBUyxDQUFDaUIsSUFBSSxDQUFDRixRQUFRLENBQUM7TUFDOUIsSUFBSSxDQUFDRyxjQUFjLENBQUMsQ0FBQztNQUNyQixJQUFJLENBQUMsQ0FBQ2pCLGFBQWEsQ0FBQ2tCLE1BQU0sQ0FBQztRQUN6QkMsSUFBSSxFQUFFLGVBQWU7UUFDckJDLFFBQVEsRUFBRSxJQUFJO1FBQ2ROO01BQ0YsQ0FBQyxDQUFDO0lBQ0o7RUFDRjtFQUNBTyxjQUFjQSxDQUFDUCxRQUFRLEVBQUU7SUFDdkIsSUFBSSxDQUFDLENBQUNmLFNBQVMsR0FBRyxJQUFJLENBQUMsQ0FBQ0EsU0FBUyxDQUFDdUIsTUFBTSxDQUFFQyxDQUFDLElBQUtBLENBQUMsS0FBS1QsUUFBUSxDQUFDO0lBQy9ELElBQUksQ0FBQ0wsVUFBVSxDQUFDLENBQUM7SUFDakIsSUFBSSxDQUFDLENBQUNULGFBQWEsQ0FBQ2tCLE1BQU0sQ0FBQztNQUN6QkMsSUFBSSxFQUFFLGlCQUFpQjtNQUN2QkMsUUFBUSxFQUFFLElBQUk7TUFDZE47SUFDRixDQUFDLENBQUM7RUFDSjtFQUNBVSxjQUFjQSxDQUFBLEVBQUc7SUFDZixJQUFJLENBQUMsSUFBSSxDQUFDLENBQUN6QixTQUFTLENBQUMwQixNQUFNLEVBQUU7TUFDM0IsSUFBSSxJQUFJLENBQUNwQixLQUFLLENBQUNxQixNQUFNLEtBQUssU0FBUyxFQUFFO1FBQ25DLElBQUksQ0FBQ2pCLFVBQVUsQ0FBQyxDQUFDO01BQ25CLENBQUMsTUFBTTtRQUNMLElBQUksQ0FBQyxDQUFDVCxhQUFhLENBQUMyQixNQUFNLENBQUMsSUFBSSxDQUFDO01BQ2xDO0lBQ0Y7RUFDRjtFQUNBQyxRQUFRQSxDQUFBLEVBQUc7SUFDVCxPQUFPLElBQUksQ0FBQyxDQUFDM0IsT0FBTyxFQUFFMkIsUUFBUSxDQUFDLENBQUM7SUFBSTtJQUNwQyxJQUFJLENBQUNDLE9BQU8sQ0FBQyxJQUFJLENBQUN4QixLQUFLLENBQUN5QixTQUFTLENBQUM7RUFDcEM7RUFDQSxNQUFNRCxPQUFPQSxDQUFDQyxTQUFTLEVBQUU7SUFDdkIsTUFBTUMsVUFBVSxHQUFHQSxDQUFBLEtBQU07TUFDdkIsSUFBSSxDQUFDLENBQUNDLFFBQVEsQ0FBQztRQUFFYixJQUFJLEVBQUU7TUFBVyxDQUFDLENBQUM7SUFDdEMsQ0FBQztJQUNELElBQUksQ0FBQyxDQUFDbEIsT0FBTyxHQUFHSiwwREFBYSxDQUFDO01BQzVCb0MsRUFBRSxFQUFFQSxDQUFBLEtBQU07UUFDUixJQUFJLENBQUMsSUFBSSxDQUFDekIsT0FBTyxDQUFDMEIsVUFBVSxFQUFFO1VBQzVCLE9BQU9DLE9BQU8sQ0FBQ0MsTUFBTSxDQUFDLElBQUlDLEtBQUssQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1FBQ3pEO1FBQ0EsT0FBTyxJQUFJLENBQUM3QixPQUFPLENBQUMwQixVQUFVLENBQUNKLFNBQVMsQ0FBQztNQUMzQyxDQUFDO01BQ0RRLE1BQU0sRUFBRUEsQ0FBQ0MsWUFBWSxFQUFFQyxLQUFLLEtBQUs7UUFDL0IsSUFBSSxDQUFDLENBQUNSLFFBQVEsQ0FBQztVQUFFYixJQUFJLEVBQUUsUUFBUTtVQUFFb0IsWUFBWTtVQUFFQztRQUFNLENBQUMsQ0FBQztNQUN6RCxDQUFDO01BQ0RDLE9BQU8sRUFBRUEsQ0FBQSxLQUFNO1FBQ2IsSUFBSSxDQUFDLENBQUNULFFBQVEsQ0FBQztVQUFFYixJQUFJLEVBQUU7UUFBUSxDQUFDLENBQUM7TUFDbkMsQ0FBQztNQUNEWSxVQUFVO01BQ1ZXLEtBQUssRUFBRSxJQUFJLENBQUNsQyxPQUFPLENBQUNrQyxLQUFLLElBQUksQ0FBQztNQUM5QkMsVUFBVSxFQUFFLElBQUksQ0FBQ25DLE9BQU8sQ0FBQ21DLFVBQVU7TUFDbkNDLFdBQVcsRUFBRSxJQUFJLENBQUNwQyxPQUFPLENBQUNvQyxXQUFXO01BQ3JDQyxNQUFNLEVBQUVBLENBQUEsS0FBTSxJQUFJLENBQUMsQ0FBQzdDLGFBQWEsQ0FBQzZDLE1BQU0sQ0FBQyxJQUFJO0lBQy9DLENBQUMsQ0FBQztJQUNGLE1BQU1DLFFBQVEsR0FBRyxJQUFJLENBQUN6QyxLQUFLLENBQUNxQixNQUFNLEtBQUssU0FBUztJQUNoRCxNQUFNcUIsUUFBUSxHQUFHLENBQUMsSUFBSSxDQUFDLENBQUM5QyxPQUFPLENBQUMrQyxRQUFRLENBQUMsQ0FBQztJQUMxQyxJQUFJO01BQ0YsSUFBSUYsUUFBUSxFQUFFO1FBQ1pmLFVBQVUsQ0FBQyxDQUFDO01BQ2QsQ0FBQyxNQUFNO1FBQ0wsSUFBSSxDQUFDLENBQUNDLFFBQVEsQ0FBQztVQUFFYixJQUFJLEVBQUUsU0FBUztVQUFFVyxTQUFTO1VBQUVpQjtRQUFTLENBQUMsQ0FBQztRQUN4RCxNQUFNLElBQUksQ0FBQyxDQUFDL0MsYUFBYSxDQUFDRyxNQUFNLENBQUM4QyxRQUFRLEdBQ3ZDbkIsU0FBUyxFQUNULElBQ0YsQ0FBQztRQUNELE1BQU1vQixPQUFPLEdBQUcsTUFBTSxJQUFJLENBQUMxQyxPQUFPLENBQUN5QyxRQUFRLEdBQUduQixTQUFTLENBQUM7UUFDeEQsSUFBSW9CLE9BQU8sS0FBSyxJQUFJLENBQUM3QyxLQUFLLENBQUM2QyxPQUFPLEVBQUU7VUFDbEMsSUFBSSxDQUFDLENBQUNsQixRQUFRLENBQUM7WUFDYmIsSUFBSSxFQUFFLFNBQVM7WUFDZitCLE9BQU87WUFDUHBCLFNBQVM7WUFDVGlCO1VBQ0YsQ0FBQyxDQUFDO1FBQ0o7TUFDRjtNQUNBLE1BQU1JLElBQUksR0FBRyxNQUFNLElBQUksQ0FBQyxDQUFDbEQsT0FBTyxDQUFDbUQsS0FBSyxDQUFDLENBQUM7TUFDeEMsTUFBTSxJQUFJLENBQUMsQ0FBQ3BELGFBQWEsQ0FBQ0csTUFBTSxDQUFDa0QsU0FBUyxHQUN4Q0YsSUFBSSxFQUNKckIsU0FBUyxFQUNULElBQUksQ0FBQ3pCLEtBQUssQ0FBQzZDLE9BQU8sRUFDbEIsSUFDRixDQUFDO01BQ0QsTUFBTSxJQUFJLENBQUMxQyxPQUFPLENBQUM2QyxTQUFTLEdBQUdGLElBQUksRUFBRXJCLFNBQVMsRUFBRSxJQUFJLENBQUN6QixLQUFLLENBQUM2QyxPQUFPLENBQUM7TUFDbkUsTUFBTSxJQUFJLENBQUMsQ0FBQ2xELGFBQWEsQ0FBQ0csTUFBTSxDQUFDbUQsU0FBUyxHQUN4Q0gsSUFBSSxFQUNKLElBQUksRUFDSixJQUFJLENBQUM5QyxLQUFLLENBQUN5QixTQUFTLEVBQ3BCLElBQUksQ0FBQ3pCLEtBQUssQ0FBQzZDLE9BQU8sRUFDbEIsSUFDRixDQUFDO01BQ0QsTUFBTSxJQUFJLENBQUMxQyxPQUFPLENBQUM4QyxTQUFTLEdBQUdILElBQUksRUFBRSxJQUFJLEVBQUVyQixTQUFTLEVBQUUsSUFBSSxDQUFDekIsS0FBSyxDQUFDNkMsT0FBTyxDQUFDO01BQ3pFLElBQUksQ0FBQyxDQUFDbEIsUUFBUSxDQUFDO1FBQUViLElBQUksRUFBRSxTQUFTO1FBQUVnQztNQUFLLENBQUMsQ0FBQztNQUN6QyxPQUFPQSxJQUFJO0lBQ2IsQ0FBQyxDQUFDLE9BQU9YLEtBQUssRUFBRTtNQUNkLElBQUk7UUFDRixNQUFNLElBQUksQ0FBQyxDQUFDeEMsYUFBYSxDQUFDRyxNQUFNLENBQUNvRCxPQUFPLEdBQ3RDZixLQUFLLEVBQ0xWLFNBQVMsRUFDVCxJQUFJLENBQUN6QixLQUFLLENBQUM2QyxPQUFPLEVBQ2xCLElBQ0YsQ0FBQztRQUNELE1BQU0sSUFBSSxDQUFDMUMsT0FBTyxDQUFDK0MsT0FBTyxHQUN4QmYsS0FBSyxFQUNMVixTQUFTLEVBQ1QsSUFBSSxDQUFDekIsS0FBSyxDQUFDNkMsT0FDYixDQUFDO1FBQ0QsTUFBTSxJQUFJLENBQUMsQ0FBQ2xELGFBQWEsQ0FBQ0csTUFBTSxDQUFDbUQsU0FBUyxHQUN4QyxLQUFLLENBQUMsRUFDTmQsS0FBSyxFQUNMLElBQUksQ0FBQ25DLEtBQUssQ0FBQ3lCLFNBQVMsRUFDcEIsSUFBSSxDQUFDekIsS0FBSyxDQUFDNkMsT0FBTyxFQUNsQixJQUNGLENBQUM7UUFDRCxNQUFNLElBQUksQ0FBQzFDLE9BQU8sQ0FBQzhDLFNBQVMsR0FDMUIsS0FBSyxDQUFDLEVBQ05kLEtBQUssRUFDTFYsU0FBUyxFQUNULElBQUksQ0FBQ3pCLEtBQUssQ0FBQzZDLE9BQ2IsQ0FBQztRQUNELE1BQU1WLEtBQUs7TUFDYixDQUFDLFNBQVM7UUFDUixJQUFJLENBQUMsQ0FBQ1IsUUFBUSxDQUFDO1VBQUViLElBQUksRUFBRSxPQUFPO1VBQUVxQjtRQUFNLENBQUMsQ0FBQztNQUMxQztJQUNGLENBQUMsU0FBUztNQUNSLElBQUksQ0FBQyxDQUFDeEMsYUFBYSxDQUFDd0QsT0FBTyxDQUFDLElBQUksQ0FBQztJQUNuQztFQUNGO0VBQ0EsQ0FBQ3hCLFFBQVF5QixDQUFDQyxNQUFNLEVBQUU7SUFDaEIsTUFBTUMsT0FBTyxHQUFJdEQsS0FBSyxJQUFLO01BQ3pCLFFBQVFxRCxNQUFNLENBQUN2QyxJQUFJO1FBQ2pCLEtBQUssUUFBUTtVQUNYLE9BQU87WUFDTCxHQUFHZCxLQUFLO1lBQ1JrQyxZQUFZLEVBQUVtQixNQUFNLENBQUNuQixZQUFZO1lBQ2pDcUIsYUFBYSxFQUFFRixNQUFNLENBQUNsQjtVQUN4QixDQUFDO1FBQ0gsS0FBSyxPQUFPO1VBQ1YsT0FBTztZQUNMLEdBQUduQyxLQUFLO1lBQ1IwQyxRQUFRLEVBQUU7VUFDWixDQUFDO1FBQ0gsS0FBSyxVQUFVO1VBQ2IsT0FBTztZQUNMLEdBQUcxQyxLQUFLO1lBQ1IwQyxRQUFRLEVBQUU7VUFDWixDQUFDO1FBQ0gsS0FBSyxTQUFTO1VBQ1osT0FBTztZQUNMLEdBQUcxQyxLQUFLO1lBQ1I2QyxPQUFPLEVBQUVRLE1BQU0sQ0FBQ1IsT0FBTztZQUN2QkMsSUFBSSxFQUFFLEtBQUssQ0FBQztZQUNaWixZQUFZLEVBQUUsQ0FBQztZQUNmcUIsYUFBYSxFQUFFLElBQUk7WUFDbkJwQixLQUFLLEVBQUUsSUFBSTtZQUNYTyxRQUFRLEVBQUVXLE1BQU0sQ0FBQ1gsUUFBUTtZQUN6QnJCLE1BQU0sRUFBRSxTQUFTO1lBQ2pCSSxTQUFTLEVBQUU0QixNQUFNLENBQUM1QixTQUFTO1lBQzNCK0IsV0FBVyxFQUFFQyxJQUFJLENBQUNDLEdBQUcsQ0FBQztVQUN4QixDQUFDO1FBQ0gsS0FBSyxTQUFTO1VBQ1osT0FBTztZQUNMLEdBQUcxRCxLQUFLO1lBQ1I4QyxJQUFJLEVBQUVPLE1BQU0sQ0FBQ1AsSUFBSTtZQUNqQlosWUFBWSxFQUFFLENBQUM7WUFDZnFCLGFBQWEsRUFBRSxJQUFJO1lBQ25CcEIsS0FBSyxFQUFFLElBQUk7WUFDWGQsTUFBTSxFQUFFLFNBQVM7WUFDakJxQixRQUFRLEVBQUU7VUFDWixDQUFDO1FBQ0gsS0FBSyxPQUFPO1VBQ1YsT0FBTztZQUNMLEdBQUcxQyxLQUFLO1lBQ1I4QyxJQUFJLEVBQUUsS0FBSyxDQUFDO1lBQ1pYLEtBQUssRUFBRWtCLE1BQU0sQ0FBQ2xCLEtBQUs7WUFDbkJELFlBQVksRUFBRWxDLEtBQUssQ0FBQ2tDLFlBQVksR0FBRyxDQUFDO1lBQ3BDcUIsYUFBYSxFQUFFRixNQUFNLENBQUNsQixLQUFLO1lBQzNCTyxRQUFRLEVBQUUsS0FBSztZQUNmckIsTUFBTSxFQUFFO1VBQ1YsQ0FBQztNQUNMO0lBQ0YsQ0FBQztJQUNELElBQUksQ0FBQ3JCLEtBQUssR0FBR3NELE9BQU8sQ0FBQyxJQUFJLENBQUN0RCxLQUFLLENBQUM7SUFDaENWLDREQUFhLENBQUNxRSxLQUFLLENBQUMsTUFBTTtNQUN4QixJQUFJLENBQUMsQ0FBQ2pFLFNBQVMsQ0FBQ2tFLE9BQU8sQ0FBRW5ELFFBQVEsSUFBSztRQUNwQ0EsUUFBUSxDQUFDb0QsZ0JBQWdCLENBQUNSLE1BQU0sQ0FBQztNQUNuQyxDQUFDLENBQUM7TUFDRixJQUFJLENBQUMsQ0FBQzFELGFBQWEsQ0FBQ2tCLE1BQU0sQ0FBQztRQUN6QkUsUUFBUSxFQUFFLElBQUk7UUFDZEQsSUFBSSxFQUFFLFNBQVM7UUFDZnVDO01BQ0YsQ0FBQyxDQUFDO0lBQ0osQ0FBQyxDQUFDO0VBQ0o7QUFDRixDQUFDO0FBQ0QsU0FBU3BELGVBQWVBLENBQUEsRUFBRztFQUN6QixPQUFPO0lBQ0w0QyxPQUFPLEVBQUUsS0FBSyxDQUFDO0lBQ2ZDLElBQUksRUFBRSxLQUFLLENBQUM7SUFDWlgsS0FBSyxFQUFFLElBQUk7SUFDWEQsWUFBWSxFQUFFLENBQUM7SUFDZnFCLGFBQWEsRUFBRSxJQUFJO0lBQ25CYixRQUFRLEVBQUUsS0FBSztJQUNmckIsTUFBTSxFQUFFLE1BQU07SUFDZEksU0FBUyxFQUFFLEtBQUssQ0FBQztJQUNqQitCLFdBQVcsRUFBRTtFQUNmLENBQUM7QUFDSCIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXEB0YW5zdGFja1xccXVlcnktY29yZVxcYnVpbGRcXG1vZGVyblxcbXV0YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL211dGF0aW9uLnRzXG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgUmVtb3ZhYmxlIH0gZnJvbSBcIi4vcmVtb3ZhYmxlLmpzXCI7XG5pbXBvcnQgeyBjcmVhdGVSZXRyeWVyIH0gZnJvbSBcIi4vcmV0cnllci5qc1wiO1xudmFyIE11dGF0aW9uID0gY2xhc3MgZXh0ZW5kcyBSZW1vdmFibGUge1xuICAjb2JzZXJ2ZXJzO1xuICAjbXV0YXRpb25DYWNoZTtcbiAgI3JldHJ5ZXI7XG4gIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgIHN1cGVyKCk7XG4gICAgdGhpcy5tdXRhdGlvbklkID0gY29uZmlnLm11dGF0aW9uSWQ7XG4gICAgdGhpcy4jbXV0YXRpb25DYWNoZSA9IGNvbmZpZy5tdXRhdGlvbkNhY2hlO1xuICAgIHRoaXMuI29ic2VydmVycyA9IFtdO1xuICAgIHRoaXMuc3RhdGUgPSBjb25maWcuc3RhdGUgfHwgZ2V0RGVmYXVsdFN0YXRlKCk7XG4gICAgdGhpcy5zZXRPcHRpb25zKGNvbmZpZy5vcHRpb25zKTtcbiAgICB0aGlzLnNjaGVkdWxlR2MoKTtcbiAgfVxuICBzZXRPcHRpb25zKG9wdGlvbnMpIHtcbiAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIHRoaXMudXBkYXRlR2NUaW1lKHRoaXMub3B0aW9ucy5nY1RpbWUpO1xuICB9XG4gIGdldCBtZXRhKCkge1xuICAgIHJldHVybiB0aGlzLm9wdGlvbnMubWV0YTtcbiAgfVxuICBhZGRPYnNlcnZlcihvYnNlcnZlcikge1xuICAgIGlmICghdGhpcy4jb2JzZXJ2ZXJzLmluY2x1ZGVzKG9ic2VydmVyKSkge1xuICAgICAgdGhpcy4jb2JzZXJ2ZXJzLnB1c2gob2JzZXJ2ZXIpO1xuICAgICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICAgICAgdGhpcy4jbXV0YXRpb25DYWNoZS5ub3RpZnkoe1xuICAgICAgICB0eXBlOiBcIm9ic2VydmVyQWRkZWRcIixcbiAgICAgICAgbXV0YXRpb246IHRoaXMsXG4gICAgICAgIG9ic2VydmVyXG4gICAgICB9KTtcbiAgICB9XG4gIH1cbiAgcmVtb3ZlT2JzZXJ2ZXIob2JzZXJ2ZXIpIHtcbiAgICB0aGlzLiNvYnNlcnZlcnMgPSB0aGlzLiNvYnNlcnZlcnMuZmlsdGVyKCh4KSA9PiB4ICE9PSBvYnNlcnZlcik7XG4gICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gICAgdGhpcy4jbXV0YXRpb25DYWNoZS5ub3RpZnkoe1xuICAgICAgdHlwZTogXCJvYnNlcnZlclJlbW92ZWRcIixcbiAgICAgIG11dGF0aW9uOiB0aGlzLFxuICAgICAgb2JzZXJ2ZXJcbiAgICB9KTtcbiAgfVxuICBvcHRpb25hbFJlbW92ZSgpIHtcbiAgICBpZiAoIXRoaXMuI29ic2VydmVycy5sZW5ndGgpIHtcbiAgICAgIGlmICh0aGlzLnN0YXRlLnN0YXR1cyA9PT0gXCJwZW5kaW5nXCIpIHtcbiAgICAgICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLiNtdXRhdGlvbkNhY2hlLnJlbW92ZSh0aGlzKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgY29udGludWUoKSB7XG4gICAgcmV0dXJuIHRoaXMuI3JldHJ5ZXI/LmNvbnRpbnVlKCkgPz8gLy8gY29udGludWluZyBhIG11dGF0aW9uIGFzc3VtZXMgdGhhdCB2YXJpYWJsZXMgYXJlIHNldCwgbXV0YXRpb24gbXVzdCBoYXZlIGJlZW4gZGVoeWRyYXRlZCBiZWZvcmVcbiAgICB0aGlzLmV4ZWN1dGUodGhpcy5zdGF0ZS52YXJpYWJsZXMpO1xuICB9XG4gIGFzeW5jIGV4ZWN1dGUodmFyaWFibGVzKSB7XG4gICAgY29uc3Qgb25Db250aW51ZSA9ICgpID0+IHtcbiAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJjb250aW51ZVwiIH0pO1xuICAgIH07XG4gICAgdGhpcy4jcmV0cnllciA9IGNyZWF0ZVJldHJ5ZXIoe1xuICAgICAgZm46ICgpID0+IHtcbiAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMubXV0YXRpb25Gbikge1xuICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChuZXcgRXJyb3IoXCJObyBtdXRhdGlvbkZuIGZvdW5kXCIpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5vcHRpb25zLm11dGF0aW9uRm4odmFyaWFibGVzKTtcbiAgICAgIH0sXG4gICAgICBvbkZhaWw6IChmYWlsdXJlQ291bnQsIGVycm9yKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJmYWlsZWRcIiwgZmFpbHVyZUNvdW50LCBlcnJvciB9KTtcbiAgICAgIH0sXG4gICAgICBvblBhdXNlOiAoKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJwYXVzZVwiIH0pO1xuICAgICAgfSxcbiAgICAgIG9uQ29udGludWUsXG4gICAgICByZXRyeTogdGhpcy5vcHRpb25zLnJldHJ5ID8/IDAsXG4gICAgICByZXRyeURlbGF5OiB0aGlzLm9wdGlvbnMucmV0cnlEZWxheSxcbiAgICAgIG5ldHdvcmtNb2RlOiB0aGlzLm9wdGlvbnMubmV0d29ya01vZGUsXG4gICAgICBjYW5SdW46ICgpID0+IHRoaXMuI211dGF0aW9uQ2FjaGUuY2FuUnVuKHRoaXMpXG4gICAgfSk7XG4gICAgY29uc3QgcmVzdG9yZWQgPSB0aGlzLnN0YXRlLnN0YXR1cyA9PT0gXCJwZW5kaW5nXCI7XG4gICAgY29uc3QgaXNQYXVzZWQgPSAhdGhpcy4jcmV0cnllci5jYW5TdGFydCgpO1xuICAgIHRyeSB7XG4gICAgICBpZiAocmVzdG9yZWQpIHtcbiAgICAgICAgb25Db250aW51ZSgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcInBlbmRpbmdcIiwgdmFyaWFibGVzLCBpc1BhdXNlZCB9KTtcbiAgICAgICAgYXdhaXQgdGhpcy4jbXV0YXRpb25DYWNoZS5jb25maWcub25NdXRhdGU/LihcbiAgICAgICAgICB2YXJpYWJsZXMsXG4gICAgICAgICAgdGhpc1xuICAgICAgICApO1xuICAgICAgICBjb25zdCBjb250ZXh0ID0gYXdhaXQgdGhpcy5vcHRpb25zLm9uTXV0YXRlPy4odmFyaWFibGVzKTtcbiAgICAgICAgaWYgKGNvbnRleHQgIT09IHRoaXMuc3RhdGUuY29udGV4dCkge1xuICAgICAgICAgIHRoaXMuI2Rpc3BhdGNoKHtcbiAgICAgICAgICAgIHR5cGU6IFwicGVuZGluZ1wiLFxuICAgICAgICAgICAgY29udGV4dCxcbiAgICAgICAgICAgIHZhcmlhYmxlcyxcbiAgICAgICAgICAgIGlzUGF1c2VkXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLiNyZXRyeWVyLnN0YXJ0KCk7XG4gICAgICBhd2FpdCB0aGlzLiNtdXRhdGlvbkNhY2hlLmNvbmZpZy5vblN1Y2Nlc3M/LihcbiAgICAgICAgZGF0YSxcbiAgICAgICAgdmFyaWFibGVzLFxuICAgICAgICB0aGlzLnN0YXRlLmNvbnRleHQsXG4gICAgICAgIHRoaXNcbiAgICAgICk7XG4gICAgICBhd2FpdCB0aGlzLm9wdGlvbnMub25TdWNjZXNzPy4oZGF0YSwgdmFyaWFibGVzLCB0aGlzLnN0YXRlLmNvbnRleHQpO1xuICAgICAgYXdhaXQgdGhpcy4jbXV0YXRpb25DYWNoZS5jb25maWcub25TZXR0bGVkPy4oXG4gICAgICAgIGRhdGEsXG4gICAgICAgIG51bGwsXG4gICAgICAgIHRoaXMuc3RhdGUudmFyaWFibGVzLFxuICAgICAgICB0aGlzLnN0YXRlLmNvbnRleHQsXG4gICAgICAgIHRoaXNcbiAgICAgICk7XG4gICAgICBhd2FpdCB0aGlzLm9wdGlvbnMub25TZXR0bGVkPy4oZGF0YSwgbnVsbCwgdmFyaWFibGVzLCB0aGlzLnN0YXRlLmNvbnRleHQpO1xuICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcInN1Y2Nlc3NcIiwgZGF0YSB9KTtcbiAgICAgIHJldHVybiBkYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCB0aGlzLiNtdXRhdGlvbkNhY2hlLmNvbmZpZy5vbkVycm9yPy4oXG4gICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgdmFyaWFibGVzLFxuICAgICAgICAgIHRoaXMuc3RhdGUuY29udGV4dCxcbiAgICAgICAgICB0aGlzXG4gICAgICAgICk7XG4gICAgICAgIGF3YWl0IHRoaXMub3B0aW9ucy5vbkVycm9yPy4oXG4gICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgdmFyaWFibGVzLFxuICAgICAgICAgIHRoaXMuc3RhdGUuY29udGV4dFxuICAgICAgICApO1xuICAgICAgICBhd2FpdCB0aGlzLiNtdXRhdGlvbkNhY2hlLmNvbmZpZy5vblNldHRsZWQ/LihcbiAgICAgICAgICB2b2lkIDAsXG4gICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgdGhpcy5zdGF0ZS52YXJpYWJsZXMsXG4gICAgICAgICAgdGhpcy5zdGF0ZS5jb250ZXh0LFxuICAgICAgICAgIHRoaXNcbiAgICAgICAgKTtcbiAgICAgICAgYXdhaXQgdGhpcy5vcHRpb25zLm9uU2V0dGxlZD8uKFxuICAgICAgICAgIHZvaWQgMCxcbiAgICAgICAgICBlcnJvcixcbiAgICAgICAgICB2YXJpYWJsZXMsXG4gICAgICAgICAgdGhpcy5zdGF0ZS5jb250ZXh0XG4gICAgICAgICk7XG4gICAgICAgIHRocm93IGVycm9yO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImVycm9yXCIsIGVycm9yIH0pO1xuICAgICAgfVxuICAgIH0gZmluYWxseSB7XG4gICAgICB0aGlzLiNtdXRhdGlvbkNhY2hlLnJ1bk5leHQodGhpcyk7XG4gICAgfVxuICB9XG4gICNkaXNwYXRjaChhY3Rpb24pIHtcbiAgICBjb25zdCByZWR1Y2VyID0gKHN0YXRlKSA9PiB7XG4gICAgICBzd2l0Y2ggKGFjdGlvbi50eXBlKSB7XG4gICAgICAgIGNhc2UgXCJmYWlsZWRcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBmYWlsdXJlQ291bnQ6IGFjdGlvbi5mYWlsdXJlQ291bnQsXG4gICAgICAgICAgICBmYWlsdXJlUmVhc29uOiBhY3Rpb24uZXJyb3JcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwicGF1c2VcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBpc1BhdXNlZDogdHJ1ZVxuICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgXCJjb250aW51ZVwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGlzUGF1c2VkOiBmYWxzZVxuICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgXCJwZW5kaW5nXCI6XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgICAgY29udGV4dDogYWN0aW9uLmNvbnRleHQsXG4gICAgICAgICAgICBkYXRhOiB2b2lkIDAsXG4gICAgICAgICAgICBmYWlsdXJlQ291bnQ6IDAsXG4gICAgICAgICAgICBmYWlsdXJlUmVhc29uOiBudWxsLFxuICAgICAgICAgICAgZXJyb3I6IG51bGwsXG4gICAgICAgICAgICBpc1BhdXNlZDogYWN0aW9uLmlzUGF1c2VkLFxuICAgICAgICAgICAgc3RhdHVzOiBcInBlbmRpbmdcIixcbiAgICAgICAgICAgIHZhcmlhYmxlczogYWN0aW9uLnZhcmlhYmxlcyxcbiAgICAgICAgICAgIHN1Ym1pdHRlZEF0OiBEYXRlLm5vdygpXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcInN1Y2Nlc3NcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBkYXRhOiBhY3Rpb24uZGF0YSxcbiAgICAgICAgICAgIGZhaWx1cmVDb3VudDogMCxcbiAgICAgICAgICAgIGZhaWx1cmVSZWFzb246IG51bGwsXG4gICAgICAgICAgICBlcnJvcjogbnVsbCxcbiAgICAgICAgICAgIHN0YXR1czogXCJzdWNjZXNzXCIsXG4gICAgICAgICAgICBpc1BhdXNlZDogZmFsc2VcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwiZXJyb3JcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBkYXRhOiB2b2lkIDAsXG4gICAgICAgICAgICBlcnJvcjogYWN0aW9uLmVycm9yLFxuICAgICAgICAgICAgZmFpbHVyZUNvdW50OiBzdGF0ZS5mYWlsdXJlQ291bnQgKyAxLFxuICAgICAgICAgICAgZmFpbHVyZVJlYXNvbjogYWN0aW9uLmVycm9yLFxuICAgICAgICAgICAgaXNQYXVzZWQ6IGZhbHNlLFxuICAgICAgICAgICAgc3RhdHVzOiBcImVycm9yXCJcbiAgICAgICAgICB9O1xuICAgICAgfVxuICAgIH07XG4gICAgdGhpcy5zdGF0ZSA9IHJlZHVjZXIodGhpcy5zdGF0ZSk7XG4gICAgbm90aWZ5TWFuYWdlci5iYXRjaCgoKSA9PiB7XG4gICAgICB0aGlzLiNvYnNlcnZlcnMuZm9yRWFjaCgob2JzZXJ2ZXIpID0+IHtcbiAgICAgICAgb2JzZXJ2ZXIub25NdXRhdGlvblVwZGF0ZShhY3Rpb24pO1xuICAgICAgfSk7XG4gICAgICB0aGlzLiNtdXRhdGlvbkNhY2hlLm5vdGlmeSh7XG4gICAgICAgIG11dGF0aW9uOiB0aGlzLFxuICAgICAgICB0eXBlOiBcInVwZGF0ZWRcIixcbiAgICAgICAgYWN0aW9uXG4gICAgICB9KTtcbiAgICB9KTtcbiAgfVxufTtcbmZ1bmN0aW9uIGdldERlZmF1bHRTdGF0ZSgpIHtcbiAgcmV0dXJuIHtcbiAgICBjb250ZXh0OiB2b2lkIDAsXG4gICAgZGF0YTogdm9pZCAwLFxuICAgIGVycm9yOiBudWxsLFxuICAgIGZhaWx1cmVDb3VudDogMCxcbiAgICBmYWlsdXJlUmVhc29uOiBudWxsLFxuICAgIGlzUGF1c2VkOiBmYWxzZSxcbiAgICBzdGF0dXM6IFwiaWRsZVwiLFxuICAgIHZhcmlhYmxlczogdm9pZCAwLFxuICAgIHN1Ym1pdHRlZEF0OiAwXG4gIH07XG59XG5leHBvcnQge1xuICBNdXRhdGlvbixcbiAgZ2V0RGVmYXVsdFN0YXRlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bXV0YXRpb24uanMubWFwIl0sIm5hbWVzIjpbIm5vdGlmeU1hbmFnZXIiLCJSZW1vdmFibGUiLCJjcmVhdGVSZXRyeWVyIiwiTXV0YXRpb24iLCJvYnNlcnZlcnMiLCJtdXRhdGlvbkNhY2hlIiwicmV0cnllciIsImNvbnN0cnVjdG9yIiwiY29uZmlnIiwibXV0YXRpb25JZCIsInN0YXRlIiwiZ2V0RGVmYXVsdFN0YXRlIiwic2V0T3B0aW9ucyIsIm9wdGlvbnMiLCJzY2hlZHVsZUdjIiwidXBkYXRlR2NUaW1lIiwiZ2NUaW1lIiwibWV0YSIsImFkZE9ic2VydmVyIiwib2JzZXJ2ZXIiLCJpbmNsdWRlcyIsInB1c2giLCJjbGVhckdjVGltZW91dCIsIm5vdGlmeSIsInR5cGUiLCJtdXRhdGlvbiIsInJlbW92ZU9ic2VydmVyIiwiZmlsdGVyIiwieCIsIm9wdGlvbmFsUmVtb3ZlIiwibGVuZ3RoIiwic3RhdHVzIiwicmVtb3ZlIiwiY29udGludWUiLCJleGVjdXRlIiwidmFyaWFibGVzIiwib25Db250aW51ZSIsImRpc3BhdGNoIiwiZm4iLCJtdXRhdGlvbkZuIiwiUHJvbWlzZSIsInJlamVjdCIsIkVycm9yIiwib25GYWlsIiwiZmFpbHVyZUNvdW50IiwiZXJyb3IiLCJvblBhdXNlIiwicmV0cnkiLCJyZXRyeURlbGF5IiwibmV0d29ya01vZGUiLCJjYW5SdW4iLCJyZXN0b3JlZCIsImlzUGF1c2VkIiwiY2FuU3RhcnQiLCJvbk11dGF0ZSIsImNvbnRleHQiLCJkYXRhIiwic3RhcnQiLCJvblN1Y2Nlc3MiLCJvblNldHRsZWQiLCJvbkVycm9yIiwicnVuTmV4dCIsIiNkaXNwYXRjaCIsImFjdGlvbiIsInJlZHVjZXIiLCJmYWlsdXJlUmVhc29uIiwic3VibWl0dGVkQXQiLCJEYXRlIiwibm93IiwiYmF0Y2giLCJmb3JFYWNoIiwib25NdXRhdGlvblVwZGF0ZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationObserver.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/mutationObserver.ts\n\n\n\n\nvar MutationObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(prevOptions.mutationKey) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? (0,_mutation_js__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(void 0, action.error, variables, context);\n        }\n      }\n      this.listeners.forEach(listener => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   defaultScheduler: () => (/* binding */ defaultScheduler),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nvar defaultScheduler = cb => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = callback => {\n    callback();\n  };\n  let batchNotifyFn = callback => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: callback => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: callback => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: fn => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: fn => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: fn => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = onOnline => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach(listener => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = {\n      ...this.#defaultOptions,\n      ...options\n    };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({\n      type: \"setState\",\n      state,\n      setStateOptions\n    });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(observer => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false);\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(observer => observer.getCurrentResult().isStale);\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({\n        type: \"observerAdded\",\n        query: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({\n        type: \"observerRemoved\",\n        query: this,\n        observer\n      });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({\n        type: \"invalidate\"\n      });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({\n          silent: true\n        });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = object => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n      const queryFnContext = {\n        client: this.#client,\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(queryFn, queryFnContext, this);\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      client: this.#client,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({\n        type: \"fetch\",\n        meta: context.fetchOptions?.meta\n      });\n    }\n    const onError = error => {\n      if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n        this.#cache.config.onError?.(error, this);\n        this.#cache.config.onSettled?.(this.state.data, error, this);\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: data => {\n        if (data === void 0) {\n          if (true) {\n            console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(data, this.state.error, this);\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue: () => {\n        this.#dispatch({\n          type: \"continue\"\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...(!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n        case \"error\":\n          const error = action.error;\n          if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n            return {\n              ...this.#revertState,\n              fetchStatus: \"idle\"\n            };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({\n        query: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n    ...(data === void 0 && {\n      error: null,\n      status: \"pending\"\n    })\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryObserver.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/queryObserver.ts\n\n\n\n\n\n\nvar QueryObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(new Error(\"experimental_prefetchInRender feature flag is not enabled\"));\n    }\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\"Expected enabled to be a boolean or a callback that returns a boolean\");\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(this.#currentQuery, prevQuery, this.options, prevOptions)) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(this.options.staleTime, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({\n    ...options\n  } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(this.options, fetchOptions);\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_2__.noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(this.options.staleTime, this.#currentQuery);\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || this.#currentResult.isStale || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(staleTime)) {\n      return;\n    }\n    const time = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) === false || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || _focusManager_js__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const {\n      state\n    } = query;\n    let newState = {\n      ...state\n    };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...(0,_query_js__WEBPACK_IMPORTED_MODULE_4__.fetchState)(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let {\n      error,\n      errorUpdatedAt,\n      status\n    } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(this.#lastQueryWithDefinedData?.state.data, this.#lastQueryWithDefinedData) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(prevResult?.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = thenable => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(notifyOnChangePropsValue ?? this.#trackedProps);\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({\n      listeners: shouldNotifyListeners()\n    });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(listener => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false) {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3));\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {}\n  onUnsubscribe() {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable)\n/* harmony export */ });\n// src/thenable.ts\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {});\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = value => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = reason => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer =  true || 0;\nfunction noop() {}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    status,\n    predicate,\n    mutationKey\n  } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every(key => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItems.includes(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (true) {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`);\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (true) {\n    if (options.queryFn === skipToken) {\n      console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`);\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ // src/IsRestoringProvider.ts\n\nvar IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nvar useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9Jc1Jlc3RvcmluZ1Byb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozt3RkFFQTtBQUM4QjtBQUM5QixJQUFJQyxrQkFBa0IsaUJBQUdELGdEQUFtQixDQUFDLEtBQUssQ0FBQztBQUNuRCxJQUFJRyxjQUFjLEdBQUdBLENBQUEsR0FBTUgsNkNBQWdCLENBQUNDLGtCQUFrQixDQUFDO0FBQy9ELElBQUlJLG1CQUFtQixHQUFHSixrQkFBa0IsQ0FBQ0ssUUFBUTtBQUdyQyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxub2RlX21vZHVsZXNcXEB0YW5zdGFja1xccmVhY3QtcXVlcnlcXGJ1aWxkXFxtb2Rlcm5cXElzUmVzdG9yaW5nUHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbi8vIHNyYy9Jc1Jlc3RvcmluZ1Byb3ZpZGVyLnRzXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciBJc1Jlc3RvcmluZ0NvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KGZhbHNlKTtcbnZhciB1c2VJc1Jlc3RvcmluZyA9ICgpID0+IFJlYWN0LnVzZUNvbnRleHQoSXNSZXN0b3JpbmdDb250ZXh0KTtcbnZhciBJc1Jlc3RvcmluZ1Byb3ZpZGVyID0gSXNSZXN0b3JpbmdDb250ZXh0LlByb3ZpZGVyO1xuZXhwb3J0IHtcbiAgSXNSZXN0b3JpbmdQcm92aWRlcixcbiAgdXNlSXNSZXN0b3Jpbmdcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Jc1Jlc3RvcmluZ1Byb3ZpZGVyLmpzLm1hcCJdLCJuYW1lcyI6WyJSZWFjdCIsIklzUmVzdG9yaW5nQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VJc1Jlc3RvcmluZyIsInVzZUNvbnRleHQiLCJJc1Jlc3RvcmluZ1Byb3ZpZGVyIiwiUHJvdmlkZXIiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"QueryClientProvider.useEffect\": ()=>{\n            client.mount();\n            return ({\n                \"QueryClientProvider.useEffect\": ()=>{\n                    client.unmount();\n                }\n            })[\"QueryClientProvider.useEffect\"];\n        }\n    }[\"QueryClientProvider.useEffect\"], [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ // src/QueryErrorResetBoundary.tsx\n\n\nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue());\nvar useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"QueryErrorResetBoundary.useState\": ()=>createValue()\n    }[\"QueryErrorResetBoundary.useState\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryErrorResetBoundaryContext.Provider, {\n        value,\n        children: typeof children === \"function\" ? children(value) : children\n    });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ // src/errorBoundaryUtils.ts\n\n\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useClearResetErrorBoundary.useEffect\": ()=>{\n            errorResetBoundary.clearReset();\n        }\n    }[\"useClearResetErrorBoundary.useEffect\"], [\n        errorResetBoundary\n    ]);\n};\nvar getHasError = ({ result, errorResetBoundary, throwOnError, query, suspense })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(throwOnError, [\n        result.error,\n        query\n    ]));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/suspense.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThrowOnError: () => (/* binding */ defaultThrowOnError),\n/* harmony export */   ensureSuspenseTimers: () => (/* binding */ ensureSuspenseTimers),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\n// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = defaultedOptions => {\n  const originalStaleTime = defaultedOptions.staleTime;\n  if (defaultedOptions.suspense) {\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => Math.max(originalStaleTime(...args), 1e3) : Math.max(originalStaleTime ?? 1e3, 1e3);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./IsRestoringProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ // src/useBaseQuery.ts\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const isRestoring = (0,_IsRestoringProvider_js__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedOptions = client.defaultQueryOptions(options);\n    client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n    if (true) {\n        if (!defaultedOptions.queryFn) {\n            console.error(`[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`);\n        }\n    }\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useBaseQuery.useState\": ()=>new Observer(client, defaultedOptions)\n    }[\"useBaseQuery.useState\"]);\n    const result = observer.getOptimisticResult(defaultedOptions);\n    const shouldSubscribe = !isRestoring && options.subscribed !== false;\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useBaseQuery.useSyncExternalStore.useCallback\": (onStoreChange)=>{\n            const unsubscribe = shouldSubscribe ? observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange)) : _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop;\n            observer.updateResult();\n            return unsubscribe;\n        }\n    }[\"useBaseQuery.useSyncExternalStore.useCallback\"], [\n        observer,\n        shouldSubscribe\n    ]), {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"], {\n        \"useBaseQuery.useSyncExternalStore\": ()=>observer.getCurrentResult()\n    }[\"useBaseQuery.useSyncExternalStore\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useBaseQuery.useEffect\": ()=>{\n            observer.setOptions(defaultedOptions);\n        }\n    }[\"useBaseQuery.useEffect\"], [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash),\n        suspense: defaultedOptions.suspense\n    })) {\n        throw result.error;\n    }\n    ;\n    client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n    if (defaultedOptions.experimental_prefetchInRender && !_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.isServer && (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n        const promise = isNewCacheEntry ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary) : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise;\n        promise?.catch(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.noop).finally(()=>{\n            observer.updateResult();\n        });\n    }\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useMutation.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* __next_internal_client_entry_do_not_use__ useMutation auto */ // src/useMutation.ts\n\n\n\nfunction useMutation(options, queryClient) {\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"useMutation.useState\": ()=>new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.MutationObserver(client, options)\n    }[\"useMutation.useState\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useMutation.useEffect\": ()=>{\n            observer.setOptions(options);\n        }\n    }[\"useMutation.useEffect\"], [\n        observer,\n        options\n    ]);\n    const result = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useMutation.useSyncExternalStore[result]\": (onStoreChange)=>observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(onStoreChange))\n    }[\"useMutation.useSyncExternalStore[result]\"], [\n        observer\n    ]), {\n        \"useMutation.useSyncExternalStore[result]\": ()=>observer.getCurrentResult()\n    }[\"useMutation.useSyncExternalStore[result]\"], {\n        \"useMutation.useSyncExternalStore[result]\": ()=>observer.getCurrentResult()\n    }[\"useMutation.useSyncExternalStore[result]\"]);\n    const mutate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useMutation.useCallback[mutate]\": (variables, mutateOptions)=>{\n            observer.mutate(variables, mutateOptions).catch(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.noop);\n        }\n    }[\"useMutation.useCallback[mutate]\"], [\n        observer\n    ]);\n    if (result.error && (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(observer.options.throwOnError, [\n        result.error\n    ])) {\n        throw result.error;\n    }\n    return {\n        ...result,\n        mutate,\n        mutateAsync: result.mutate\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useQuery.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ // src/useQuery.ts\n\n\nfunction useQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.QueryObserver, queryClient);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91c2VRdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OERBRUE7QUFDb0Q7QUFDSjtBQUNoRCxTQUFTRSxRQUFRQSxDQUFDQyxPQUFPLEVBQUVDLFdBQVcsRUFBRTtJQUN0QyxPQUFPSCw4REFBWSxDQUFDRSxPQUFPLEVBQUVILCtEQUFhLEVBQUVJLFdBQVcsQ0FBQztBQUMxRDtBQUVVIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXG5vZGVfbW9kdWxlc1xcQHRhbnN0YWNrXFxyZWFjdC1xdWVyeVxcYnVpbGRcXG1vZGVyblxcdXNlUXVlcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbi8vIHNyYy91c2VRdWVyeS50c1xuaW1wb3J0IHsgUXVlcnlPYnNlcnZlciB9IGZyb20gXCJAdGFuc3RhY2svcXVlcnktY29yZVwiO1xuaW1wb3J0IHsgdXNlQmFzZVF1ZXJ5IH0gZnJvbSBcIi4vdXNlQmFzZVF1ZXJ5LmpzXCI7XG5mdW5jdGlvbiB1c2VRdWVyeShvcHRpb25zLCBxdWVyeUNsaWVudCkge1xuICByZXR1cm4gdXNlQmFzZVF1ZXJ5KG9wdGlvbnMsIFF1ZXJ5T2JzZXJ2ZXIsIHF1ZXJ5Q2xpZW50KTtcbn1cbmV4cG9ydCB7XG4gIHVzZVF1ZXJ5XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlUXVlcnkuanMubWFwIl0sIm5hbWVzIjpbIlF1ZXJ5T2JzZXJ2ZXIiLCJ1c2VCYXNlUXVlcnkiLCJ1c2VRdWVyeSIsIm9wdGlvbnMiLCJxdWVyeUNsaWVudCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\n");

/***/ })

};
;