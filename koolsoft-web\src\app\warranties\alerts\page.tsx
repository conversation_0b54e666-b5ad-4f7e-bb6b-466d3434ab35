'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  AlertTriangle, 
  Search, 
  Filter, 
  FileDown, 
  Bell, 
  Eye, 
  Mail, 
  Clock,
  Calendar,
  CheckCircle,
  XCircle,
  Settings,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';

/**
 * Warranty Alerts Page
 * 
 * This page displays warranty expiration alerts and notifications.
 * It includes alert management, notification settings, and alert history.
 */
export default function WarrantyAlertsPage() {
  const [alerts, setAlerts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('active');

  // Mock data for development
  useEffect(() => {
    const loadAlerts = async () => {
      try {
        setIsLoading(true);
        // TODO: Replace with actual API call
        // const response = await fetch('/api/warranties/alerts');
        // const data = await response.json();
        
        // Mock data for now
        const mockData = [
          {
            id: '1',
            type: 'WARRANTY_EXPIRING',
            priority: 'HIGH',
            title: 'Warranty Expiring Soon',
            message: 'ABC Corporation warranty (BSL001) expires in 5 days',
            warrantyId: 'w1',
            customerId: 'c1',
            customer: { name: 'ABC Corporation', city: 'Mumbai' },
            expiryDate: '2024-01-25',
            daysRemaining: 5,
            status: 'ACTIVE',
            createdAt: '2024-01-20T10:00:00Z',
            notificationsSent: 2,
            lastNotificationAt: '2024-01-20T10:00:00Z'
          },
          {
            id: '2',
            type: 'WARRANTY_EXPIRED',
            priority: 'CRITICAL',
            title: 'Warranty Expired',
            message: 'XYZ Industries warranty (BSL002) has expired',
            warrantyId: 'w2',
            customerId: 'c2',
            customer: { name: 'XYZ Industries', city: 'Delhi' },
            expiryDate: '2024-01-18',
            daysRemaining: -2,
            status: 'ACTIVE',
            createdAt: '2024-01-18T09:00:00Z',
            notificationsSent: 3,
            lastNotificationAt: '2024-01-19T09:00:00Z'
          },
          {
            id: '3',
            type: 'COMPONENT_EXPIRING',
            priority: 'MEDIUM',
            title: 'Component Warranty Expiring',
            message: 'Component COMP001 warranty expires in 15 days',
            warrantyId: 'w1',
            componentId: 'comp1',
            customerId: 'c1',
            customer: { name: 'ABC Corporation', city: 'Mumbai' },
            expiryDate: '2024-02-05',
            daysRemaining: 15,
            status: 'ACKNOWLEDGED',
            createdAt: '2024-01-15T14:00:00Z',
            notificationsSent: 1,
            lastNotificationAt: '2024-01-15T14:00:00Z'
          }
        ];
        
        setAlerts(mockData);
        setError(null);
      } catch (err) {
        console.error('Error loading alerts:', err);
        setError('Failed to load warranty alerts');
      } finally {
        setIsLoading(false);
      }
    };

    loadAlerts();
  }, []);

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'CRITICAL':
        return <Badge variant="destructive" className="flex items-center space-x-1">
          <AlertTriangle className="h-3 w-3" />
          <span>Critical</span>
        </Badge>;
      case 'HIGH':
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800 flex items-center space-x-1">
          <AlertTriangle className="h-3 w-3" />
          <span>High</span>
        </Badge>;
      case 'MEDIUM':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 flex items-center space-x-1">
          <Clock className="h-3 w-3" />
          <span>Medium</span>
        </Badge>;
      default:
        return <Badge variant="secondary" className="flex items-center space-x-1">
          <Clock className="h-3 w-3" />
          <span>Low</span>
        </Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Active</Badge>;
      case 'ACKNOWLEDGED':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Acknowledged</Badge>;
      case 'RESOLVED':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Resolved</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'WARRANTY_EXPIRING':
      case 'WARRANTY_EXPIRED':
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'COMPONENT_EXPIRING':
      case 'COMPONENT_EXPIRED':
        return <Settings className="h-4 w-4 text-blue-600" />;
      default:
        return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN');
  };

  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = searchTerm === '' || 
      alert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.customer.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesPriority = priorityFilter === 'all' || alert.priority === priorityFilter;
    const matchesType = typeFilter === 'all' || alert.type === typeFilter;
    
    let matchesTab = true;
    if (activeTab === 'active') matchesTab = alert.status === 'ACTIVE';
    else if (activeTab === 'acknowledged') matchesTab = alert.status === 'ACKNOWLEDGED';
    else if (activeTab === 'resolved') matchesTab = alert.status === 'RESOLVED';
    
    return matchesSearch && matchesPriority && matchesType && matchesTab;
  });

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export alerts data');
  };

  const handleRefresh = () => {
    // TODO: Implement refresh functionality
    console.log('Refresh alerts');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Warranty Alerts</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Monitor warranty expiration alerts and manage notifications
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="secondary" onClick={handleExport}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="secondary" asChild>
              <Link href="/warranties/alerts/settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="active">Active Alerts</TabsTrigger>
              <TabsTrigger value="acknowledged">Acknowledged</TabsTrigger>
              <TabsTrigger value="resolved">Resolved</TabsTrigger>
              <TabsTrigger value="all">All Alerts</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-4">
              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="space-y-2">
                  <Label htmlFor="search" className="text-black">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search alerts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority" className="text-black">Priority</Label>
                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger id="priority">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Priorities</SelectItem>
                      <SelectItem value="CRITICAL">Critical</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="LOW">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type" className="text-black">Alert Type</Label>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="WARRANTY_EXPIRING">Warranty Expiring</SelectItem>
                      <SelectItem value="WARRANTY_EXPIRED">Warranty Expired</SelectItem>
                      <SelectItem value="COMPONENT_EXPIRING">Component Expiring</SelectItem>
                      <SelectItem value="COMPONENT_EXPIRED">Component Expired</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Error State */}
              {error && (
                <Alert className="mb-6">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-black">{error}</AlertDescription>
                </Alert>
              )}

              {/* Alerts Table */}
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-black">Alert</TableHead>
                      <TableHead className="text-black">Customer</TableHead>
                      <TableHead className="text-black">Expiry Date</TableHead>
                      <TableHead className="text-black">Days Remaining</TableHead>
                      <TableHead className="text-black">Priority</TableHead>
                      <TableHead className="text-black">Status</TableHead>
                      <TableHead className="text-black">Notifications</TableHead>
                      <TableHead className="text-right text-black">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      // Loading skeleton
                      Array.from({ length: 5 }).map((_, index) => (
                        <TableRow key={`skeleton-${index}`}>
                          <TableCell><Skeleton className="h-6 w-48" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                          <TableCell className="text-right"><Skeleton className="h-6 w-16 ml-auto" /></TableCell>
                        </TableRow>
                      ))
                    ) : filteredAlerts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8">
                          <div className="flex flex-col items-center space-y-2">
                            <Bell className="h-8 w-8 text-gray-400" />
                            <p className="text-gray-500">No alerts found</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredAlerts.map((alert) => (
                        <TableRow key={alert.id}>
                          <TableCell className="text-black">
                            <div className="flex items-start space-x-3">
                              {getTypeIcon(alert.type)}
                              <div>
                                <div className="font-medium">{alert.title}</div>
                                <div className="text-sm text-gray-500">{alert.message}</div>
                                <div className="text-xs text-gray-400">{formatDateTime(alert.createdAt)}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">
                            <div>
                              <div className="font-medium">{alert.customer.name}</div>
                              <div className="text-sm text-gray-500">{alert.customer.city}</div>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span>{formatDate(alert.expiryDate)}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">
                            {alert.daysRemaining > 0 ? (
                              <div className="flex items-center space-x-1 text-orange-600">
                                <Clock className="h-4 w-4" />
                                <span>{alert.daysRemaining} days</span>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-1 text-red-600">
                                <XCircle className="h-4 w-4" />
                                <span>Expired</span>
                              </div>
                            )}
                          </TableCell>
                          <TableCell>{getPriorityBadge(alert.priority)}</TableCell>
                          <TableCell>{getStatusBadge(alert.status)}</TableCell>
                          <TableCell className="text-black">
                            <div className="flex items-center space-x-1">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <span>{alert.notificationsSent}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end space-x-2">
                              <Button variant="ghost" size="sm" asChild>
                                <Link href={`/warranties/alerts/${alert.id}`}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </Button>
                              {alert.status === 'ACTIVE' && (
                                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                                  <CheckCircle className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination would go here */}
              {!isLoading && filteredAlerts.length > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <p className="text-sm text-gray-600">
                    Showing {filteredAlerts.length} of {alerts.length} alerts
                  </p>
                  {/* TODO: Add pagination controls */}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
