'use client';

import { useState, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

export interface Payment {
  id: string;
  amcContractId: string;
  receiptNo?: string;
  paymentDate: Date;
  paymentMode?: string;
  amount: number;
  particulars?: string;
  originalAmcId?: number;
  originalReceiptNo?: number;
  createdAt: Date;
  updatedAt: Date;
  amcContract?: {
    id: string;
    customer: {
      id: string;
      name: string;
    };
  };
}

export interface PaymentFilters {
  amcContractId?: string;
  paymentMode?: string;
  dateFrom?: Date;
  dateTo?: Date;
  amountMin?: number;
  amountMax?: number;
  skip?: number;
  take?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface CreatePaymentData {
  amcContractId: string;
  paymentDate: Date;
  amount: number;
  paymentMode: 'CASH' | 'CHEQUE' | 'BANK_TRANSFER' | 'ONLINE';
  receiptNo?: string;
  particulars?: string;
}

export interface UpdatePaymentData {
  paymentDate?: Date;
  amount?: number;
  paymentMode?: 'CASH' | 'CHEQUE' | 'BANK_TRANSFER' | 'ONLINE';
  receiptNo?: string;
  particulars?: string;
}

export interface PaymentStatistics {
  totalPaid: number;
  paymentCount: number;
  lastPaymentDate: Date | null;
  averagePaymentAmount: number;
  paymentModes: { mode: string; count: number; total: number }[];
}

/**
 * Hook for managing payments
 */
export function usePayments(filters?: PaymentFilters) {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);

  // Build query parameters
  const queryParams = new URLSearchParams();
  if (filters?.amcContractId) queryParams.set('amcContractId', filters.amcContractId);
  if (filters?.paymentMode) queryParams.set('paymentMode', filters.paymentMode);
  if (filters?.dateFrom) queryParams.set('dateFrom', filters.dateFrom.toISOString());
  if (filters?.dateTo) queryParams.set('dateTo', filters.dateTo.toISOString());
  if (filters?.amountMin !== undefined) queryParams.set('amountMin', filters.amountMin.toString());
  if (filters?.amountMax !== undefined) queryParams.set('amountMax', filters.amountMax.toString());
  if (filters?.skip !== undefined) queryParams.set('skip', filters.skip.toString());
  if (filters?.take !== undefined) queryParams.set('take', filters.take.toString());
  if (filters?.orderBy) queryParams.set('orderBy', filters.orderBy);
  if (filters?.orderDirection) queryParams.set('orderDirection', filters.orderDirection);

  // Fetch payments
  const {
    data: paymentsData,
    isLoading: isLoadingPayments,
    error: paymentsError,
    refetch: refetchPayments,
  } = useQuery({
    queryKey: ['payments', queryParams.toString()],
    queryFn: async () => {
      const response = await fetch(`/api/amc/payments?${queryParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }

      return response.json();
    },
    enabled: true,
  });

  // Create payment mutation
  const createPaymentMutation = useMutation({
    mutationFn: async (data: CreatePaymentData) => {
      const response = await fetch('/api/amc/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create payment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['amc-contracts'] });
      showSuccessToast('Payment created successfully');
    },
    onError: (error: Error) => {
      showErrorToast('Failed to create payment', error.message);
    },
  });

  // Update payment mutation
  const updatePaymentMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdatePaymentData }) => {
      const response = await fetch(`/api/amc/payments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update payment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['amc-contracts'] });
      showSuccessToast('Payment updated successfully');
    },
    onError: (error: Error) => {
      showErrorToast('Failed to update payment', error.message);
    },
  });

  // Delete payment mutation
  const deletePaymentMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/amc/payments/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete payment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['amc-contracts'] });
      showSuccessToast('Payment deleted successfully');
    },
    onError: (error: Error) => {
      showErrorToast('Failed to delete payment', error.message);
    },
  });

  // Generate receipt number
  const generateReceiptNumber = useCallback(async (): Promise<string> => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/amc/payments/generate-receipt-number', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to generate receipt number');
      }

      const data = await response.json();
      return data.receiptNumber;
    } catch (error) {
      showErrorToast('Failed to generate receipt number');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Validate receipt number
  const validateReceiptNumber = useCallback(async (receiptNo: string, excludeId?: string): Promise<boolean> => {
    try {
      const params = new URLSearchParams({ receiptNo });
      if (excludeId) params.set('excludeId', excludeId);

      const response = await fetch(`/api/amc/payments/validate-receipt?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to validate receipt number');
      }

      const data = await response.json();
      return data.isUnique;
    } catch (error) {
      console.error('Error validating receipt number:', error);
      return false;
    }
  }, []);

  return {
    // Data
    payments: paymentsData?.payments || [],
    total: paymentsData?.total || 0,
    
    // Loading states
    isLoading: isLoadingPayments || isLoading,
    isCreating: createPaymentMutation.isPending,
    isUpdating: updatePaymentMutation.isPending,
    isDeleting: deletePaymentMutation.isPending,
    
    // Error states
    error: paymentsError,
    
    // Actions
    createPayment: createPaymentMutation.mutate,
    updatePayment: updatePaymentMutation.mutate,
    deletePayment: deletePaymentMutation.mutate,
    refetch: refetchPayments,
    generateReceiptNumber,
    validateReceiptNumber,
  };
}

/**
 * Hook for fetching payment statistics for a contract
 */
export function usePaymentStatistics(amcContractId: string) {
  return useQuery({
    queryKey: ['payment-statistics', amcContractId],
    queryFn: async (): Promise<PaymentStatistics> => {
      const response = await fetch(`/api/amc/contracts/${amcContractId}/payment-statistics`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment statistics');
      }

      return response.json();
    },
    enabled: !!amcContractId,
  });
}

/**
 * Hook for fetching payments for a specific contract
 */
export function useContractPayments(amcContractId: string) {
  return usePayments({ amcContractId });
}
