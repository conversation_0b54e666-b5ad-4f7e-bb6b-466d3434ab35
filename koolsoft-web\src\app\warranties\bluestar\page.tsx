'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  Search, 
  Filter, 
  FileDown, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  Phone,
  MapPin,
  FileText
} from 'lucide-react';
import Link from 'next/link';

/**
 * BLUESTAR Warranty Management Page
 * 
 * This page displays and manages BLUESTAR-specific warranty workflows.
 * It includes vendor-specific forms, validation rules, and specialized reporting.
 */
export default function BluestarWarrantyPage() {
  const [bluestarWarranties, setBluestarWarranties] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [serviceCenterFilter, setServiceCenterFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('warranties');

  // Mock data for development
  useEffect(() => {
    const loadBluestarWarranties = async () => {
      try {
        setIsLoading(true);
        // TODO: Replace with actual API call
        // const response = await fetch('/api/warranties?vendor=bluestar');
        // const data = await response.json();
        
        // Mock data for now
        const mockData = [
          {
            id: '1',
            bslNo: 'BSL-BS001',
            customer: { name: 'ABC Corporation', city: 'Mumbai' },
            installDate: '2024-01-15',
            warrantyDate: '2025-01-15',
            status: 'ACTIVE',
            numberOfMachines: 2,
            bslAmount: 200000,
            vendorSpecific: {
              bluestarWarrantyCode: 'BS-WC-2024-001',
              bluestarServiceCenter: 'Mumbai Central Service Center',
              bluestarContactPerson: 'Rajesh Kumar',
              bluestarContactPhone: '+91-9876543210',
              specialTerms: 'Extended warranty for commercial use'
            },
            machines: [
              { 
                serialNumber: 'BS001', 
                product: { name: 'BLUESTAR Air Conditioner' }, 
                model: { name: 'BS-AC-5T' },
                location: 'Office Floor 1'
              }
            ]
          },
          {
            id: '2',
            bslNo: 'BSL-BS002',
            customer: { name: 'XYZ Industries', city: 'Delhi' },
            installDate: '2024-02-20',
            warrantyDate: '2024-12-20',
            status: 'ACTIVE',
            numberOfMachines: 1,
            bslAmount: 150000,
            vendorSpecific: {
              bluestarWarrantyCode: 'BS-WC-2024-002',
              bluestarServiceCenter: 'Delhi North Service Center',
              bluestarContactPerson: 'Amit Sharma',
              bluestarContactPhone: '+91-9876543211',
              specialTerms: 'Standard commercial warranty'
            },
            machines: [
              { 
                serialNumber: 'BS002', 
                product: { name: 'BLUESTAR Refrigerator' }, 
                model: { name: 'BS-REF-300L' },
                location: 'Warehouse'
              }
            ]
          }
        ];
        
        setBluestarWarranties(mockData);
        setError(null);
      } catch (err) {
        console.error('Error loading BLUESTAR warranties:', err);
        setError('Failed to load BLUESTAR warranty data');
      } finally {
        setIsLoading(false);
      }
    };

    loadBluestarWarranties();
  }, []);

  const getStatusBadge = (status: string, warrantyDate: string) => {
    const today = new Date();
    const warranty = new Date(warrantyDate);
    const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (status === 'EXPIRED') {
      return <Badge variant="destructive" className="flex items-center space-x-1">
        <AlertTriangle className="h-3 w-3" />
        <span>Expired</span>
      </Badge>;
    }

    if (daysUntilExpiry <= 30) {
      return <Badge variant="secondary" className="flex items-center space-x-1 bg-yellow-100 text-yellow-800">
        <Clock className="h-3 w-3" />
        <span>Expiring Soon</span>
      </Badge>;
    }

    return <Badge variant="secondary" className="flex items-center space-x-1 bg-green-100 text-green-800">
      <CheckCircle className="h-3 w-3" />
      <span>Active</span>
    </Badge>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const filteredWarranties = bluestarWarranties.filter(warranty => {
    const matchesSearch = searchTerm === '' || 
      warranty.bslNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      warranty.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      warranty.vendorSpecific.bluestarWarrantyCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      warranty.machines.some((machine: any) => 
        machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesStatus = statusFilter === 'all' || warranty.status === statusFilter;
    const matchesServiceCenter = serviceCenterFilter === 'all' || 
      warranty.vendorSpecific.bluestarServiceCenter === serviceCenterFilter;
    
    return matchesSearch && matchesStatus && matchesServiceCenter;
  });

  const handleExport = () => {
    // TODO: Implement BLUESTAR-specific export functionality
    console.log('Export BLUESTAR warranty data');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>BLUESTAR Warranty Management</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Manage vendor-specific BLUESTAR warranty workflows and specialized processes
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleExport}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="secondary" asChild>
              <Link href="/warranties/new?vendor=bluestar">
                <Plus className="h-4 w-4 mr-2" />
                New BLUESTAR Warranty
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs defaultValue="warranties" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="warranties">Warranties</TabsTrigger>
              <TabsTrigger value="service-centers">Service Centers</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
            </TabsList>

            <TabsContent value="warranties" className="space-y-4">
              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="space-y-2">
                  <Label htmlFor="search" className="text-black">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search by BSL No, customer, or warranty code..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status" className="text-black">Status</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger id="status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="EXPIRED">Expired</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="serviceCenter" className="text-black">Service Center</Label>
                  <Select value={serviceCenterFilter} onValueChange={setServiceCenterFilter}>
                    <SelectTrigger id="serviceCenter">
                      <SelectValue placeholder="Select service center" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Service Centers</SelectItem>
                      <SelectItem value="Mumbai Central Service Center">Mumbai Central</SelectItem>
                      <SelectItem value="Delhi North Service Center">Delhi North</SelectItem>
                      {/* TODO: Populate with actual service centers */}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Error State */}
              {error && (
                <Alert className="mb-6">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-black">{error}</AlertDescription>
                </Alert>
              )}

              {/* BLUESTAR Warranties Table */}
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-black">BSL No / Warranty Code</TableHead>
                      <TableHead className="text-black">Customer</TableHead>
                      <TableHead className="text-black">Machine</TableHead>
                      <TableHead className="text-black">Service Center</TableHead>
                      <TableHead className="text-black">Warranty Date</TableHead>
                      <TableHead className="text-black">Amount</TableHead>
                      <TableHead className="text-black">Status</TableHead>
                      <TableHead className="text-right text-black">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      // Loading skeleton
                      Array.from({ length: 5 }).map((_, index) => (
                        <TableRow key={`skeleton-${index}`}>
                          <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                          <TableCell className="text-right"><Skeleton className="h-6 w-16 ml-auto" /></TableCell>
                        </TableRow>
                      ))
                    ) : filteredWarranties.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8">
                          <div className="flex flex-col items-center space-y-2">
                            <Shield className="h-8 w-8 text-gray-400" />
                            <p className="text-gray-500">No BLUESTAR warranties found</p>
                            <Button asChild>
                              <Link href="/warranties/new?vendor=bluestar">
                                <Plus className="h-4 w-4 mr-2" />
                                Add First BLUESTAR Warranty
                              </Link>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredWarranties.map((warranty) => (
                        <TableRow key={warranty.id}>
                          <TableCell className="text-black">
                            <div>
                              <div className="font-medium">{warranty.bslNo}</div>
                              <div className="text-sm text-gray-500">{warranty.vendorSpecific.bluestarWarrantyCode}</div>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">
                            <div>
                              <div className="font-medium">{warranty.customer.name}</div>
                              <div className="text-sm text-gray-500">{warranty.customer.city}</div>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">
                            <div>
                              <div className="font-medium">{warranty.machines[0]?.product.name}</div>
                              <div className="text-sm text-gray-500">Model: {warranty.machines[0]?.model.name}</div>
                              <div className="text-sm text-gray-500">SN: {warranty.machines[0]?.serialNumber}</div>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">
                            <div>
                              <div className="font-medium flex items-center space-x-1">
                                <MapPin className="h-3 w-3 text-gray-400" />
                                <span className="text-sm">{warranty.vendorSpecific.bluestarServiceCenter}</span>
                              </div>
                              <div className="text-sm text-gray-500 flex items-center space-x-1">
                                <Phone className="h-3 w-3 text-gray-400" />
                                <span>{warranty.vendorSpecific.bluestarContactPerson}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span>{formatDate(warranty.warrantyDate)}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">{formatCurrency(warranty.bslAmount)}</TableCell>
                          <TableCell>{getStatusBadge(warranty.status, warranty.warrantyDate)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end space-x-2">
                              <Button variant="ghost" size="sm" asChild>
                                <Link href={`/warranties/bluestar/${warranty.id}`}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </Button>
                              <Button variant="ghost" size="sm" asChild>
                                <Link href={`/warranties/bluestar/${warranty.id}/edit`}>
                                  <Edit className="h-4 w-4" />
                                </Link>
                              </Button>
                              <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination would go here */}
              {!isLoading && filteredWarranties.length > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <p className="text-sm text-gray-600">
                    Showing {filteredWarranties.length} of {bluestarWarranties.length} BLUESTAR warranties
                  </p>
                  {/* TODO: Add pagination controls */}
                </div>
              )}
            </TabsContent>

            <TabsContent value="service-centers" className="space-y-4">
              <div className="text-center py-8">
                <MapPin className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-black mb-2">BLUESTAR Service Centers</h3>
                <p className="text-gray-600 mb-4">
                  Manage BLUESTAR service center information and contact details.
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Service Center
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="reports" className="space-y-4">
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-black mb-2">BLUESTAR Reports</h3>
                <p className="text-gray-600 mb-4">
                  Generate specialized reports for BLUESTAR warranty management.
                </p>
                <Button>
                  <FileDown className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
