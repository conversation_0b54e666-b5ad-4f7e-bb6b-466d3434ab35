import { PrismaClient, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Warranty Machine Repository
 *
 * This repository handles database operations for the Warranty Machine entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class WarrantyMachineRepository extends PrismaRepository<
  Prisma.warranty_machinesGetPayload<{}>,
  string,
  Prisma.warranty_machinesCreateInput,
  Prisma.warranty_machinesUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('warranty_machines');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  /**
   * Find warranty machines by warranty ID
   * @param warrantyId Warranty ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranty machines
   */
  async findByWarrantyId(warrantyId: string, skip?: number, take?: number): Promise<Prisma.warranty_machinesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { warrantyId },
      skip,
      take,
      include: {
        product: true,
        model: true,
        brand: true,
        components: true,
      },
    });
  }

  /**
   * Find warranty machine by serial number
   * @param serialNumber Machine serial number
   * @returns Promise resolving to the warranty machine or null if not found
   */
  async findBySerialNumber(serialNumber: string): Promise<Prisma.warranty_machinesGetPayload<{}> | null> {
    return this.model.findFirst({
      where: { serialNumber },
      include: {
        warranty: true,
        product: true,
        model: true,
        brand: true,
        components: true,
      },
    });
  }

  /**
   * Find warranty machines by product ID
   * @param productId Product ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranty machines
   */
  async findByProductId(productId: string, skip?: number, take?: number): Promise<Prisma.warranty_machinesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { productId },
      skip,
      take,
      include: {
        warranty: true,
        product: true,
        model: true,
        brand: true,
        components: true,
      },
    });
  }

  /**
   * Find warranty machines by model ID
   * @param modelId Model ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranty machines
   */
  async findByModelId(modelId: string, skip?: number, take?: number): Promise<Prisma.warranty_machinesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { modelId },
      skip,
      take,
      include: {
        warranty: true,
        product: true,
        model: true,
        brand: true,
        components: true,
      },
    });
  }

  /**
   * Find warranty machines by brand ID
   * @param brandId Brand ID
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to an array of warranty machines
   */
  async findByBrandId(brandId: string, skip?: number, take?: number): Promise<Prisma.warranty_machinesGetPayload<{}>[]> {
    return this.model.findMany({
      where: { brandId },
      skip,
      take,
      include: {
        warranty: true,
        product: true,
        model: true,
        brand: true,
        components: true,
      },
    });
  }

  /**
   * Find warranty machine with all related data
   * @param id Warranty machine ID
   * @returns Promise resolving to the warranty machine with related data or null if not found
   */
  async findWithRelations(id: string): Promise<any | null> {
    return this.model.findUnique({
      where: { id },
      include: {
        warranty: {
          include: {
            customer: true,
            contactPerson: true,
          },
        },
        product: true,
        model: true,
        brand: true,
        components: true,
      },
    });
  }

  /**
   * Find warranty machines with filter, pagination, and sorting
   * @param filter Filter condition
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @param orderBy Sorting criteria
   * @returns Promise resolving to an array of warranty machines
   */
  async findWithFilter(
    filter: any,
    skip?: number,
    take?: number,
    orderBy?: any
  ): Promise<Prisma.warranty_machinesGetPayload<{}>[]> {
    console.log('WarrantyMachineRepository.findWithFilter: Starting with params:', {
      filter,
      skip,
      take,
      orderBy
    });

    try {
      console.log('WarrantyMachineRepository.findWithFilter: Checking model availability');
      if (!this.model) {
        console.error('WarrantyMachineRepository.findWithFilter: Model is not available');
        return [];
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);

      console.log('WarrantyMachineRepository.findWithFilter: Executing findMany query');
      const result = await this.model.findMany({
        where: filter,
        skip,
        take,
        orderBy: orderBy || { createdAt: 'desc' },
        include: {
          warranty: {
            select: {
              id: true,
              bslNo: true,
              installDate: true,
              warrantyDate: true,
              status: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  city: true,
                },
              },
            },
          },
          product: {
            select: {
              id: true,
              name: true,
            },
          },
          model: {
            select: {
              id: true,
              name: true,
            },
          },
          brand: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      console.log('WarrantyMachineRepository.findWithFilter: Query completed successfully, found', result.length, 'records');
      return result;
    } catch (error) {
      console.error('WarrantyMachineRepository.findWithFilter: Error executing query:', error);
      throw error;
    }
  }

  /**
   * Count warranty machines with filter
   * @param filter Filter condition
   * @returns Promise resolving to the count
   */
  async countWithFilter(filter: any): Promise<number> {
    console.log('WarrantyMachineRepository.countWithFilter: Starting with filter:', filter);

    try {
      if (!this.model) {
        console.error('WarrantyMachineRepository.countWithFilter: Model is not available');
        return 0;
      }

      // Validate filter to ensure it only contains valid fields
      this.validateFilter(filter);

      const count = await this.model.count({
        where: filter,
      });

      console.log('WarrantyMachineRepository.countWithFilter: Count completed successfully, found', count, 'records');
      return count;
    } catch (error) {
      console.error('WarrantyMachineRepository.countWithFilter: Error executing count:', error);
      throw error;
    }
  }

  /**
   * Validate filter to ensure it only contains valid fields
   * @param filter Filter condition
   */
  private validateFilter(filter: any): void {
    if (!filter || typeof filter !== 'object') {
      return;
    }

    const validFields = [
      'id', 'warrantyId', 'productId', 'modelId', 'brandId', 'location',
      'locationFlag', 'serialNumber', 'assetNo', 'historyCardNo', 'section',
      'originalWarrantyId', 'originalAssetNo', 'originalHistoryCardNo',
      'createdAt', 'updatedAt'
    ];

    for (const key in filter) {
      if (!validFields.includes(key) && !key.includes('.')) {
        console.warn(`WarrantyMachineRepository.validateFilter: Invalid filter field '${key}' removed`);
        delete filter[key];
      }
    }
  }

  /**
   * Create warranty machine with validation
   * @param data Warranty machine data
   * @returns Promise resolving to the created warranty machine
   */
  async createWithValidation(data: any): Promise<any> {
    // Check if serial number already exists
    if (data.serialNumber) {
      const existing = await this.findBySerialNumber(data.serialNumber);
      if (existing) {
        throw new Error(`Machine with serial number ${data.serialNumber} already exists in warranty`);
      }
    }

    return this.create(data);
  }

  /**
   * Update warranty machine with validation
   * @param id Warranty machine ID
   * @param data Updated warranty machine data
   * @returns Promise resolving to the updated warranty machine
   */
  async updateWithValidation(id: string, data: any): Promise<any> {
    // Check if serial number already exists (excluding current record)
    if (data.serialNumber) {
      const existing = await this.model.findFirst({
        where: {
          serialNumber: data.serialNumber,
          NOT: { id },
        },
      });
      
      if (existing) {
        throw new Error(`Machine with serial number ${data.serialNumber} already exists in warranty`);
      }
    }

    return this.update(id, data);
  }

  /**
   * Delete warranty machine and its components
   * @param id Warranty machine ID
   * @returns Promise resolving to the deleted warranty machine
   */
  async deleteWithComponents(id: string): Promise<any> {
    return this.prisma.$transaction(async (tx) => {
      // Delete components first
      await tx.warranty_components.deleteMany({
        where: { machineId: id },
      });

      // Delete the machine
      return tx.warranty_machines.delete({
        where: { id },
      });
    });
  }
}
