import { z } from 'zod';

/**
 * Warranty Machine schema
 */
export const warrantyMachineSchema = z.object({
  id: z.string().uuid().optional(),
  productId: z.string().uuid().optional(),
  modelId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  location: z.string().optional(),
  locationFlag: z.string().max(1).optional(),
  serialNumber: z.string().min(1, 'Serial number is required').max(50),
  assetNo: z.number().int().optional(),
  historyCardNo: z.number().int().optional(),
  section: z.string().max(1).optional(),
  originalWarrantyId: z.number().int().optional(),
  originalAssetNo: z.number().int().optional(),
  originalHistoryCardNo: z.number().int().optional(),
});

/**
 * Warranty Component schema
 */
export const warrantyComponentSchema = z.object({
  id: z.string().uuid().optional(),
  machineId: z.string().uuid(),
  componentNo: z.number().int().min(1, 'Component number is required'),
  serialNumber: z.string().min(1, 'Serial number is required').max(50),
  warrantyDate: z.date().optional(),
  section: z.string().max(1).optional(),
  originalWarrantyId: z.number().int().optional(),
  originalAssetNo: z.number().int().optional(),
  originalComponentNo: z.number().int().optional(),
});

/**
 * Base warranty schema with common fields
 */
export const warrantyBaseSchema = z.object({
  customerId: z.string().uuid('Customer is required'),
  executiveId: z.string().uuid().optional(),
  contactPersonId: z.string().uuid().optional(),
  bslNo: z.string().max(12).optional(),
  bslDate: z.date().optional(),
  bslAmount: z.number().min(0, 'BSL amount must be non-negative').default(0),
  frequency: z.number().int().min(0).default(0),
  numberOfMachines: z.number().int().min(1, 'Number of machines must be at least 1').default(1),
  installDate: z.date().optional(),
  warrantyDate: z.date().optional(),
  warningDate: z.date().optional(),
  technicianId: z.string().uuid().optional(),
  amcId: z.string().uuid().optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED']).default('ACTIVE'),
});

/**
 * Warranty creation schema
 */
export const createWarrantySchema = warrantyBaseSchema.extend({
  machines: z.array(warrantyMachineSchema).optional(),
}).refine(
  (data) => {
    // Ensure warranty date is after install date if both are provided
    if (data.installDate && data.warrantyDate) {
      return data.warrantyDate >= data.installDate;
    }
    return true;
  },
  {
    message: 'Warranty date must be after install date',
    path: ['warrantyDate'],
  }
).refine(
  (data) => {
    // Ensure warning date is before warranty date if both are provided
    if (data.warningDate && data.warrantyDate) {
      return data.warningDate <= data.warrantyDate;
    }
    return true;
  },
  {
    message: 'Warning date must be before or equal to warranty date',
    path: ['warningDate'],
  }
);

/**
 * Warranty update schema
 */
export const updateWarrantySchema = warrantyBaseSchema.partial().extend({
  id: z.string().uuid(),
}).refine(
  (data) => {
    // Ensure warranty date is after install date if both are provided
    if (data.installDate && data.warrantyDate) {
      return data.warrantyDate >= data.installDate;
    }
    return true;
  },
  {
    message: 'Warranty date must be after install date',
    path: ['warrantyDate'],
  }
).refine(
  (data) => {
    // Ensure warning date is before warranty date if both are provided
    if (data.warningDate && data.warrantyDate) {
      return data.warningDate <= data.warrantyDate;
    }
    return true;
  },
  {
    message: 'Warning date must be before or equal to warranty date',
    path: ['warningDate'],
  }
);

/**
 * Warranty filter schema for API queries
 */
export const warrantyFilterSchema = z.object({
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED']).optional(),
  bslNo: z.string().optional(),
  serialNumber: z.string().optional(),
  installDateFrom: z.date().optional(),
  installDateTo: z.date().optional(),
  warrantyDateFrom: z.date().optional(),
  warrantyDateTo: z.date().optional(),
  search: z.string().optional(),
  skip: z.number().int().min(0).default(0),
  take: z.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['installDate', 'warrantyDate', 'bslNo', 'customer.name', 'status']).default('installDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
}).refine(
  (data) => {
    // Ensure install date range is valid
    if (data.installDateFrom && data.installDateTo) {
      return data.installDateTo >= data.installDateFrom;
    }
    return true;
  },
  {
    message: 'Install date "to" must be after "from" date',
    path: ['installDateTo'],
  }
).refine(
  (data) => {
    // Ensure warranty date range is valid
    if (data.warrantyDateFrom && data.warrantyDateTo) {
      return data.warrantyDateTo >= data.warrantyDateFrom;
    }
    return true;
  },
  {
    message: 'Warranty date "to" must be after "from" date',
    path: ['warrantyDateTo'],
  }
);

/**
 * Warranty machine filter schema
 */
export const warrantyMachineFilterSchema = z.object({
  warrantyId: z.string().uuid().optional(),
  productId: z.string().uuid().optional(),
  modelId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  serialNumber: z.string().optional(),
  location: z.string().optional(),
  search: z.string().optional(),
  skip: z.number().int().min(0).default(0),
  take: z.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['serialNumber', 'location', 'product.name', 'model.name', 'brand.name']).default('serialNumber'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * Warranty component filter schema
 */
export const warrantyComponentFilterSchema = z.object({
  machineId: z.string().uuid().optional(),
  componentNo: z.number().int().optional(),
  serialNumber: z.string().optional(),
  warrantyDateFrom: z.date().optional(),
  warrantyDateTo: z.date().optional(),
  search: z.string().optional(),
  skip: z.number().int().min(0).default(0),
  take: z.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['componentNo', 'serialNumber', 'warrantyDate']).default('componentNo'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
}).refine(
  (data) => {
    // Ensure warranty date range is valid
    if (data.warrantyDateFrom && data.warrantyDateTo) {
      return data.warrantyDateTo >= data.warrantyDateFrom;
    }
    return true;
  },
  {
    message: 'Warranty date "to" must be after "from" date',
    path: ['warrantyDateTo'],
  }
);

/**
 * Warranty status update schema
 */
export const warrantyStatusUpdateSchema = z.object({
  status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'CANCELLED']),
  reason: z.string().optional(),
});

/**
 * Warranty expiration alert schema
 */
export const warrantyExpirationAlertSchema = z.object({
  days: z.number().int().min(1).max(365).default(30),
  includeComponents: z.boolean().default(true),
  notificationTypes: z.array(z.enum(['EMAIL', 'DASHBOARD', 'SMS'])).default(['EMAIL', 'DASHBOARD']),
});

/**
 * BLUESTAR warranty schema (vendor-specific)
 */
export const bluestarWarrantySchema = warrantyBaseSchema.extend({
  vendorSpecific: z.object({
    bluestarWarrantyCode: z.string().min(1, 'BLUESTAR warranty code is required'),
    bluestarServiceCenter: z.string().optional(),
    bluestarContactPerson: z.string().optional(),
    bluestarContactPhone: z.string().optional(),
    specialTerms: z.string().optional(),
  }),
});

/**
 * Warranty export schema
 */
export const warrantyExportSchema = z.object({
  format: z.enum(['CSV', 'EXCEL', 'PDF']).default('CSV'),
  filters: warrantyFilterSchema.optional(),
  includeComponents: z.boolean().default(false),
  includeMachines: z.boolean().default(true),
});

// Type exports for TypeScript
export type WarrantyMachine = z.infer<typeof warrantyMachineSchema>;
export type WarrantyComponent = z.infer<typeof warrantyComponentSchema>;
export type CreateWarrantyData = z.infer<typeof createWarrantySchema>;
export type UpdateWarrantyData = z.infer<typeof updateWarrantySchema>;
export type WarrantyFilterData = z.infer<typeof warrantyFilterSchema>;
export type WarrantyMachineFilterData = z.infer<typeof warrantyMachineFilterSchema>;
export type WarrantyComponentFilterData = z.infer<typeof warrantyComponentFilterSchema>;
export type WarrantyStatusUpdateData = z.infer<typeof warrantyStatusUpdateSchema>;
export type WarrantyExpirationAlertData = z.infer<typeof warrantyExpirationAlertSchema>;
export type BluestarWarrantyData = z.infer<typeof bluestarWarrantySchema>;
export type WarrantyExportData = z.infer<typeof warrantyExportSchema>;
