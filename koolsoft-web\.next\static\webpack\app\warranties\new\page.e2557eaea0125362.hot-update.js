"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/new/page",{

/***/ "(app-pages-browser)/./src/app/warranties/new/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/warranties/new/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewWarrantyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * New Warranty Page\n *\n * This page provides a form for creating new warranties with support for\n * different warranty types (in-warranty, out-warranty, BLUESTAR-specific).\n */ function NewWarrantyPage() {\n    _s();\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const warrantyType = searchParams.get('type') || 'in-warranty';\n    const vendor = searchParams.get('vendor');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: '',\n        executiveId: '',\n        contactPersonId: '',\n        bslNo: '',\n        bslDate: '',\n        bslAmount: '',\n        frequency: '',\n        numberOfMachines: '1',\n        installDate: '',\n        warrantyDate: '',\n        warningDate: '',\n        technicianId: '',\n        amcId: '',\n        status: 'ACTIVE',\n        // BLUESTAR specific fields\n        bluestarWarrantyCode: '',\n        bluestarServiceCenter: '',\n        bluestarContactPerson: '',\n        bluestarContactPhone: '',\n        specialTerms: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('basic');\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [executives, setExecutives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingData, setIsLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Load customers and executives\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewWarrantyPage.useEffect\": ()=>{\n            const loadFormData = {\n                \"NewWarrantyPage.useEffect.loadFormData\": async ()=>{\n                    try {\n                        setIsLoadingData(true);\n                        const [customersRes, executivesRes] = await Promise.all([\n                            fetch('/api/customers?take=1000', {\n                                credentials: 'include'\n                            }),\n                            fetch('/api/users?role=EXECUTIVE&take=1000', {\n                                credentials: 'include'\n                            })\n                        ]);\n                        if (customersRes.ok) {\n                            const customersData = await customersRes.json();\n                            setCustomers(customersData.customers || []);\n                        }\n                        if (executivesRes.ok) {\n                            const executivesData = await executivesRes.json();\n                            setExecutives(executivesData.users || []);\n                        }\n                    } catch (error) {\n                        console.error('Error loading form data:', error);\n                    } finally{\n                        setIsLoadingData(false);\n                    }\n                }\n            }[\"NewWarrantyPage.useEffect.loadFormData\"];\n            loadFormData();\n        }\n    }[\"NewWarrantyPage.useEffect\"], []);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.customerId) {\n            newErrors.customerId = 'Customer is required';\n        }\n        if (!formData.bslNo) {\n            newErrors.bslNo = 'BSL Number is required';\n        }\n        if (!formData.numberOfMachines || parseInt(formData.numberOfMachines) < 1) {\n            newErrors.numberOfMachines = 'Number of machines must be at least 1';\n        }\n        if (formData.installDate && formData.warrantyDate) {\n            const installDate = new Date(formData.installDate);\n            const warrantyDate = new Date(formData.warrantyDate);\n            if (warrantyDate <= installDate) {\n                newErrors.warrantyDate = 'Warranty date must be after install date';\n            }\n        }\n        if (vendor === 'bluestar' && !formData.bluestarWarrantyCode) {\n            newErrors.bluestarWarrantyCode = 'BLUESTAR warranty code is required';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Prepare warranty data for API\n            const warrantyData = {\n                customerId: formData.customerId,\n                executiveId: formData.executiveId || null,\n                contactPersonId: formData.contactPersonId || null,\n                bslNo: formData.bslNo || null,\n                bslDate: formData.bslDate ? new Date(formData.bslDate).toISOString() : null,\n                bslAmount: formData.bslAmount ? parseFloat(formData.bslAmount) : 0,\n                frequency: formData.frequency ? parseInt(formData.frequency) : 0,\n                numberOfMachines: parseInt(formData.numberOfMachines),\n                installDate: formData.installDate ? new Date(formData.installDate).toISOString() : null,\n                warrantyDate: formData.warrantyDate ? new Date(formData.warrantyDate).toISOString() : null,\n                warningDate: formData.warningDate ? new Date(formData.warningDate).toISOString() : null,\n                technicianId: formData.technicianId || null,\n                amcId: formData.amcId || null,\n                status: formData.status,\n                // Add BLUESTAR specific data if applicable\n                ...vendor === 'bluestar' && {\n                    vendorSpecific: {\n                        bluestarWarrantyCode: formData.bluestarWarrantyCode,\n                        bluestarServiceCenter: formData.bluestarServiceCenter,\n                        bluestarContactPerson: formData.bluestarContactPerson,\n                        bluestarContactPhone: formData.bluestarContactPhone,\n                        specialTerms: formData.specialTerms\n                    }\n                }\n            };\n            const response = await fetch('/api/warranties', {\n                method: 'POST',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(warrantyData)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to create warranty');\n            }\n            const createdWarranty = await response.json();\n            // Redirect to appropriate page after creation\n            if (warrantyType === 'out-warranty') {\n                window.location.href = '/warranties/out-warranty';\n            } else if (vendor === 'bluestar') {\n                window.location.href = '/warranties/bluestar';\n            } else {\n                window.location.href = '/warranties/in-warranty';\n            }\n        } catch (error) {\n            console.error('Error creating warranty:', error);\n            setErrors({\n                submit: error.message || 'Failed to create warranty. Please try again.'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getPageTitle = ()=>{\n        if (vendor === 'bluestar') return 'New BLUESTAR Warranty';\n        if (warrantyType === 'out-warranty') return 'New Out-of-Warranty';\n        return 'New In-Warranty';\n    };\n    const getBackUrl = ()=>{\n        if (vendor === 'bluestar') return '/warranties/bluestar';\n        if (warrantyType === 'out-warranty') return '/warranties/out-warranty';\n        return '/warranties/in-warranty';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getPageTitle()\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Create a new warranty record with machine and component details\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"secondary\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                    href: getBackUrl(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Cancel\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                                defaultValue: \"basic\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"basic\",\n                                                children: \"Basic Information\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"dates\",\n                                                children: \"Dates & Terms\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"machines\",\n                                                children: \"Machines\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            vendor === 'bluestar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"bluestar\",\n                                                children: \"BLUESTAR Details\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"basic\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"customerId\",\n                                                            className: \"text-black\",\n                                                            children: \"Customer *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.customerId,\n                                                            onValueChange: (value)=>handleInputChange('customerId', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"customerId\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: isLoadingData ? \"Loading customers...\" : \"Select customer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: customer.id,\n                                                                                children: [\n                                                                                    customer.name,\n                                                                                    \" \",\n                                                                                    customer.city && \"(\".concat(customer.city, \")\")\n                                                                                ]\n                                                                            }, customer.id, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 234,\n                                                                                columnNumber: 52\n                                                                            }, this)),\n                                                                        customers.length === 0 && !isLoadingData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"no-customers\",\n                                                                            disabled: true,\n                                                                            children: \"No customers available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 70\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.customerId\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"executiveId\",\n                                                            className: \"text-black\",\n                                                            children: \"Executive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.executiveId,\n                                                            onValueChange: (value)=>handleInputChange('executiveId', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"executiveId\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: isLoadingData ? \"Loading executives...\" : \"Select executive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        executives.map((executive)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: executive.id,\n                                                                                children: [\n                                                                                    executive.name,\n                                                                                    \" \",\n                                                                                    executive.email && \"(\".concat(executive.email, \")\")\n                                                                                ]\n                                                                            }, executive.id, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 252,\n                                                                                columnNumber: 54\n                                                                            }, this)),\n                                                                        executives.length === 0 && !isLoadingData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"no-executives\",\n                                                                            disabled: true,\n                                                                            children: \"No executives available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 71\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslNo\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Number *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bslNo\",\n                                                            value: formData.bslNo,\n                                                            onChange: (e)=>handleInputChange('bslNo', e.target.value),\n                                                            placeholder: \"Enter BSL number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.bslNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.bslNo\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 38\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslAmount\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"bslAmount\",\n                                                                    type: \"number\",\n                                                                    value: formData.bslAmount,\n                                                                    onChange: (e)=>handleInputChange('bslAmount', e.target.value),\n                                                                    placeholder: \"0\",\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"numberOfMachines\",\n                                                            className: \"text-black\",\n                                                            children: \"Number of Machines *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"numberOfMachines\",\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            value: formData.numberOfMachines,\n                                                            onChange: (e)=>handleInputChange('numberOfMachines', e.target.value),\n                                                            placeholder: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.numberOfMachines && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.numberOfMachines\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-black\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.status,\n                                                            onValueChange: (value)=>handleInputChange('status', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"status\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"ACTIVE\",\n                                                                            children: \"Active\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"PENDING\",\n                                                                            children: \"Pending\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"EXPIRED\",\n                                                                            children: \"Expired\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"CANCELLED\",\n                                                                            children: \"Cancelled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"dates\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslDate\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"bslDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.bslDate,\n                                                                    onChange: (e)=>handleInputChange('bslDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"installDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Install Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"installDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.installDate,\n                                                                    onChange: (e)=>handleInputChange('installDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"warrantyDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Warranty Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"warrantyDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.warrantyDate,\n                                                                    onChange: (e)=>handleInputChange('warrantyDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.warrantyDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.warrantyDate\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"warningDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Warning Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"warningDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.warningDate,\n                                                                    onChange: (e)=>handleInputChange('warningDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"frequency\",\n                                                            className: \"text-black\",\n                                                            children: \"Frequency\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"frequency\",\n                                                            type: \"number\",\n                                                            value: formData.frequency,\n                                                            onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"machines\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-black mb-2\",\n                                                    children: \"Machine Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Machine and component assignment interface will be implemented here.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Add Machine\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    vendor === 'bluestar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"bluestar\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarWarrantyCode\",\n                                                            className: \"text-black\",\n                                                            children: \"BLUESTAR Warranty Code *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarWarrantyCode\",\n                                                            value: formData.bluestarWarrantyCode,\n                                                            onChange: (e)=>handleInputChange('bluestarWarrantyCode', e.target.value),\n                                                            placeholder: \"Enter BLUESTAR warranty code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.bluestarWarrantyCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.bluestarWarrantyCode\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarServiceCenter\",\n                                                            className: \"text-black\",\n                                                            children: \"Service Center\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.bluestarServiceCenter,\n                                                            onValueChange: (value)=>handleInputChange('bluestarServiceCenter', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"bluestarServiceCenter\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select service center\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Mumbai Central Service Center\",\n                                                                            children: \"Mumbai Central\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Delhi North Service Center\",\n                                                                            children: \"Delhi North\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Bangalore South Service Center\",\n                                                                            children: \"Bangalore South\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Chennai East Service Center\",\n                                                                            children: \"Chennai East\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Kolkata West Service Center\",\n                                                                            children: \"Kolkata West\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Pune Central Service Center\",\n                                                                            children: \"Pune Central\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarContactPerson\",\n                                                            className: \"text-black\",\n                                                            children: \"Contact Person\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarContactPerson\",\n                                                            value: formData.bluestarContactPerson,\n                                                            onChange: (e)=>handleInputChange('bluestarContactPerson', e.target.value),\n                                                            placeholder: \"Enter contact person name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarContactPhone\",\n                                                            className: \"text-black\",\n                                                            children: \"Contact Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarContactPhone\",\n                                                            value: formData.bluestarContactPhone,\n                                                            onChange: (e)=>handleInputChange('bluestarContactPhone', e.target.value),\n                                                            placeholder: \"Enter contact phone number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"specialTerms\",\n                                                            className: \"text-black\",\n                                                            children: \"Special Terms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"specialTerms\",\n                                                            value: formData.specialTerms,\n                                                            onChange: (e)=>handleInputChange('specialTerms', e.target.value),\n                                                            placeholder: \"Enter any special terms or conditions\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                        className: \"text-black\",\n                                        children: errors.submit\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                            href: getBackUrl(),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Creating...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create Warranty\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 10\n    }, this);\n}\n_s(NewWarrantyPage, \"oEA5x5Ns+OO7pzg4GKVLkkF1wtI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = NewWarrantyPage;\n_s1(NewWarrantyPage, \"oEA5x5Ns+OO7pzg4GKVLkkF1wtI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = NewWarrantyPage;\nvar _c;\n$RefreshReg$(_c, \"NewWarrantyPage\");\nvar _c1;\n$RefreshReg$(_c1, \"NewWarrantyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/new/page.tsx\n"));

/***/ })

});