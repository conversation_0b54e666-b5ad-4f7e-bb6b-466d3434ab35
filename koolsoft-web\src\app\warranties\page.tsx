'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Shield, AlertTriangle, CheckCircle, Clock, XCircle, FileDown, Filter } from 'lucide-react';
import Link from 'next/link';

interface WarrantyStats {
  active: number;
  expiring: number;
  expired: number;
  pending: number;
  total: number;
}

/**
 * Warranty Management Main Page
 *
 * This page provides an overview of warranty management with quick access
 * to different warranty categories and status information.
 */
export default function WarrantiesPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState<WarrantyStats>({
    active: 0,
    expiring: 0,
    expired: 0,
    pending: 0,
    total: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load warranty statistics
  useEffect(() => {
    const loadStats = async () => {
      try {
        setIsLoading(true);

        const [warrantyRes, expiringRes] = await Promise.all([
          fetch('/api/warranties?take=1000', { credentials: 'include' }),
          fetch('/api/warranties/expiring?days=30', { credentials: 'include' })
        ]);

        if (!warrantyRes.ok || !expiringRes.ok) {
          throw new Error('Failed to fetch warranty statistics');
        }

        const [warrantyData, expiringData] = await Promise.all([
          warrantyRes.json(),
          expiringRes.json()
        ]);

        const warranties = warrantyData.warranties || [];
        const expiringWarranties = expiringData.warranties || [];

        const newStats = {
          total: warranties.length,
          active: warranties.filter((w: any) => w.status === 'ACTIVE').length,
          expired: warranties.filter((w: any) => w.status === 'EXPIRED').length,
          pending: warranties.filter((w: any) => w.status === 'PENDING').length,
          expiring: expiringWarranties.length
        };

        setStats(newStats);
        setError(null);
      } catch (err) {
        console.error('Error loading warranty statistics:', err);
        setError('Failed to load warranty statistics');
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, []);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const handleExport = async () => {
    try {
      const response = await fetch('/api/warranties/export?format=CSV', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export warranties');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `all-warranties-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting warranties:', error);
      setError('Failed to export warranties');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Warranty Management</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Manage in-warranty and out-of-warranty products, track components, and monitor warranty status
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleExport}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="secondary" asChild>
              <Link href="/warranties/status">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Status Dashboard
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs defaultValue="overview" value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="in-warranty">In-Warranty</TabsTrigger>
              <TabsTrigger value="out-warranty">Out-of-Warranty</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
              <TabsTrigger value="alerts">Alerts</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">Active Warranties</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span className="text-2xl font-bold text-black">
                        {isLoading ? '...' : stats.active}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">Expiring Soon</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                      <span className="text-2xl font-bold text-black">
                        {isLoading ? '...' : stats.expiring}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">Expired</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <XCircle className="h-5 w-5 text-red-600" />
                      <span className="text-2xl font-bold text-black">
                        {isLoading ? '...' : stats.expired}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">Pending</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-5 w-5 text-blue-600" />
                      <span className="text-2xl font-bold text-black">
                        {isLoading ? '...' : stats.pending}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {error && (
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="pt-6">
                    <div className="flex items-center space-x-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      <span>{error}</span>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-black">
                      <Shield className="h-5 w-5 text-green-600" />
                      <span>In-Warranty Management</span>
                    </CardTitle>
                    <CardDescription className="text-black">
                      Manage products currently under warranty coverage
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full">
                      <Link href="/warranties/in-warranty">
                        View In-Warranty Products
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-black">
                      <XCircle className="h-5 w-5 text-red-600" />
                      <span>Out-of-Warranty Management</span>
                    </CardTitle>
                    <CardDescription className="text-black">
                      Manage products that are no longer under warranty
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full">
                      <Link href="/warranties/out-warranty">
                        View Out-of-Warranty Products
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-black">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                      <span>Component Tracking</span>
                    </CardTitle>
                    <CardDescription className="text-black">
                      Track individual components and their warranty status
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full">
                      <Link href="/warranties/components">
                        View Component Tracking
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-black">
                      <AlertTriangle className="h-5 w-5 text-orange-600" />
                      <span>Warranty Status Dashboard</span>
                    </CardTitle>
                    <CardDescription className="text-black">
                      Monitor warranty status and expiration alerts
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full">
                      <Link href="/warranties/status">
                        View Status Dashboard
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-black">
                      <Shield className="h-5 w-5 text-blue-600" />
                      <span>BLUESTAR Warranties</span>
                    </CardTitle>
                    <CardDescription className="text-black">
                      Manage vendor-specific BLUESTAR warranty workflows
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full">
                      <Link href="/warranties/bluestar">
                        View BLUESTAR Warranties
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2 text-black">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      <span>Warranty Alerts</span>
                    </CardTitle>
                    <CardDescription className="text-black">
                      View and manage warranty expiration notifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full">
                      <Link href="/warranties/alerts">
                        View Warranty Alerts
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="in-warranty" className="space-y-4">
              <div className="text-center py-8">
                <Shield className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-black mb-2">In-Warranty Management</h3>
                <p className="text-gray-600 mb-4">
                  This section will display in-warranty products and management tools.
                </p>
                <Button asChild>
                  <Link href="/warranties/in-warranty">
                    Go to In-Warranty Management
                  </Link>
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="out-warranty" className="space-y-4">
              <div className="text-center py-8">
                <XCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-black mb-2">Out-of-Warranty Management</h3>
                <p className="text-gray-600 mb-4">
                  This section will display out-of-warranty products and management tools.
                </p>
                <Button asChild>
                  <Link href="/warranties/out-warranty">
                    Go to Out-of-Warranty Management
                  </Link>
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="components" className="space-y-4">
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-black mb-2">Component Tracking</h3>
                <p className="text-gray-600 mb-4">
                  This section will display component warranty tracking tools.
                </p>
                <Button asChild>
                  <Link href="/warranties/components">
                    Go to Component Tracking
                  </Link>
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="alerts" className="space-y-4">
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-black mb-2">Warranty Alerts</h3>
                <p className="text-gray-600 mb-4">
                  This section will display warranty expiration alerts and notifications.
                </p>
                <Button asChild>
                  <Link href="/warranties/alerts">
                    Go to Warranty Alerts
                  </Link>
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
