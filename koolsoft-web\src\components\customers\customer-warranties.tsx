import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  Package, 
  ChevronRight, 
  Shield 
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import Link from 'next/link';

interface CustomerWarrantiesProps {
  warranties: any[];
}

/**
 * Customer Warranties Component
 *
 * This component displays the warranties associated with a customer.
 */
export function CustomerWarranties({ warranties }: CustomerWarrantiesProps) {
  // Function to determine warranty status badge
  const getStatusBadge = (status: string, endDate: string) => {
    const now = new Date();
    const end = new Date(endDate);
    
    if (status === 'ACTIVE' && end < now) {
      return <Badge className="bg-yellow-500 text-white">Expired</Badge>;
    }
    
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-500 text-white">Active</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-500 text-white">Pending</Badge>;
      case 'EXPIRED':
        return <Badge className="bg-gray-500 text-white">Expired</Badge>;
      case 'CANCELLED':
        return <Badge className="bg-red-500 text-white">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (!warranties || warranties.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Warranties</CardTitle>
          <CardDescription>
            Warranty information for this customer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-gray-500">
            No warranties found for this customer.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Warranties</CardTitle>
        <CardDescription>
          Warranty information for this customer
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Warranty Number</TableHead>
              <TableHead>Period</TableHead>
              <TableHead>Machines</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {warranties.map((warranty) => (
              <TableRow key={warranty.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 mr-2 text-gray-500" />
                    <span>
                      {warranty.warrantyNumber || `WTY-${warranty.id.substring(0, 8)}`}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <span>Start: {formatDate(warranty.startDate)}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <span>End: {formatDate(warranty.endDate)}</span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Package className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{warranty.machines?.length || 0} machines</span>
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(warranty.status, warranty.endDate)}
                </TableCell>
                <TableCell className="text-right">
                  <Button asChild variant="ghost" size="sm">
                    <Link href={`/warranties/${warranty.id}`}>
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
