/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/reference-data/page";
exports.ids = ["app/reference-data/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freference-data%2Fpage&page=%2Freference-data%2Fpage&appPaths=%2Freference-data%2Fpage&pagePath=private-next-app-dir%2Freference-data%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freference-data%2Fpage&page=%2Freference-data%2Fpage&appPaths=%2Freference-data%2Fpage&pagePath=private-next-app-dir%2Freference-data%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?4184\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reference-data/layout.tsx */ \"(rsc)/./src/app/reference-data/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reference-data/page.tsx */ \"(rsc)/./src/app/reference-data/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'reference-data',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/reference-data/page\",\n        pathname: \"/reference-data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freference-data%2Fpage&page=%2Freference-data%2Fpage&appPaths=%2Freference-data%2Fpage&pagePath=private-next-app-dir%2Freference-data%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(rsc)/./src/components/providers/SessionProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/custom-toaster.tsx */ \"(rsc)/./src/components/ui/custom-toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q3Byb2plY3RzJTVDJTVDS29vbFNvZnQlNUMlNUNrb29sc29mdC13ZWIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNlc3Npb25Qcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJHJTNBJTVDJTVDcHJvamVjdHMlNUMlNUNLb29sU29mdCU1QyU1Q2tvb2xzb2Z0LXdlYiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q2N1c3RvbS10b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkN1c3RvbVRvYXN0ZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUErSjtBQUMvSjtBQUNBLHdMQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2Vzc2lvblByb3ZpZGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxTZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDdXN0b21Ub2FzdGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXGN1c3RvbS10b2FzdGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reference-data/layout.tsx */ \"(rsc)/./src/app/reference-data/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcmVmZXJlbmNlLWRhdGElNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBK0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkc6XFxcXHByb2plY3RzXFxcXEtvb2xTb2Z0XFxcXGtvb2xzb2Z0LXdlYlxcXFxzcmNcXFxcYXBwXFxcXHJlZmVyZW5jZS1kYXRhXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reference-data/page.tsx */ \"(rsc)/./src/app/reference-data/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcmVmZXJlbmNlLWRhdGElNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQTZHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxwcm9qZWN0c1xcXFxLb29sU29mdFxcXFxrb29sc29mdC13ZWJcXFxcc3JjXFxcXGFwcFxcXFxyZWZlcmVuY2UtZGF0YVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b3e87d01cb8a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImIzZTg3ZDAxY2I4YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"(rsc)/./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var _components_ui_custom_toaster__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/custom-toaster */ \"(rsc)/./src/components/ui/custom-toaster.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'KoolSoft',\n    description: 'KoolSoft Management System'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_custom_toaster__WEBPACK_IMPORTED_MODULE_3__.CustomToaster, {}, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDc0I7QUFDa0Q7QUFDVjtBQUV2RCxNQUFNRSxRQUFrQixHQUFHO0lBQ2hDQyxLQUFLLEVBQUUsVUFBVTtJQUNqQkMsV0FBVyxFQUFFO0FBQ2YsQ0FBQztBQUVjLFNBQVNDLFVBQVVBLENBQUMsRUFDakNDLFFBQUFBLEVBR0EsRUFBRTtJQUNGLHFCQUNFLDhEQUFDLElBQUk7UUFBQyxJQUFJLEVBQUMsSUFBSTtnQ0FDYiw4REFBQyxJQUFJO1lBQUMsU0FBUyxFQUFDLHVCQUF1Qjs7OEJBQ3JDLDhEQUFDLGtGQUFlLENBQUM7OEJBQUNBLFFBQVE7Ozs7Ozs4QkFDMUIsOERBQUMsd0VBQWE7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdEIiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyJztcbmltcG9ydCB7IEN1c3RvbVRvYXN0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY3VzdG9tLXRvYXN0ZXInO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0tvb2xTb2Z0JyxcbiAgZGVzY3JpcHRpb246ICdLb29sU29mdCBNYW5hZ2VtZW50IFN5c3RlbScsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zIGFudGlhbGlhc2VkXCI+XG4gICAgICAgIDxTZXNzaW9uUHJvdmlkZXI+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPlxuICAgICAgICA8Q3VzdG9tVG9hc3RlciAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJDdXN0b21Ub2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/reference-data/layout.tsx":
/*!*******************************************!*\
  !*** ./src/app/reference-data/layout.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\reference-data\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/reference-data/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/reference-data/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\projects\\KoolSoft\\koolsoft-web\\src\\app\\reference-data\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SessionProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\projects\\KoolSoft\\koolsoft-web\\src\\components\\providers\\SessionProvider.tsx",
"SessionProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/custom-toaster.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/custom-toaster.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CustomToaster: () => (/* binding */ CustomToaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CustomToaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CustomToaster() from the server but CustomToaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\projects\\KoolSoft\\koolsoft-web\\src\\components\\ui\\custom-toaster.tsx",
"CustomToaster",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(ssr)/./src/components/providers/SessionProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/custom-toaster.tsx */ \"(ssr)/./src/components/ui/custom-toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRyUzQSU1QyU1Q3Byb2plY3RzJTVDJTVDS29vbFNvZnQlNUMlNUNrb29sc29mdC13ZWIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNlc3Npb25Qcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJHJTNBJTVDJTVDcHJvamVjdHMlNUMlNUNLb29sU29mdCU1QyU1Q2tvb2xzb2Z0LXdlYiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q2N1c3RvbS10b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkN1c3RvbVRvYXN0ZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUErSjtBQUMvSjtBQUNBLHdMQUFxSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2Vzc2lvblByb3ZpZGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFxTZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDdXN0b21Ub2FzdGVyXCJdICovIFwiRzpcXFxccHJvamVjdHNcXFxcS29vbFNvZnRcXFxca29vbHNvZnQtd2ViXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXGN1c3RvbS10b2FzdGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CSessionProvider.tsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ccustom-toaster.tsx%22%2C%22ids%22%3A%5B%22CustomToaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reference-data/layout.tsx */ \"(ssr)/./src/app/reference-data/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcmVmZXJlbmNlLWRhdGElNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBK0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkc6XFxcXHByb2plY3RzXFxcXEtvb2xTb2Z0XFxcXGtvb2xzb2Z0LXdlYlxcXFxzcmNcXFxcYXBwXFxcXHJlZmVyZW5jZS1kYXRhXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/reference-data/page.tsx */ \"(ssr)/./src/app/reference-data/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q0tvb2xTb2Z0JTVDJTVDa29vbHNvZnQtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcmVmZXJlbmNlLWRhdGElNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQTZHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxwcm9qZWN0c1xcXFxLb29sU29mdFxcXFxrb29sc29mdC13ZWJcXFxcc3JjXFxcXGFwcFxcXFxyZWZlcmVuY2UtZGF0YVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5Cprojects%5C%5CKoolSoft%5C%5Ckoolsoft-web%5C%5Csrc%5C%5Capp%5C%5Creference-data%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/reference-data/layout.tsx":
/*!*******************************************!*\
  !*** ./src/app/reference-data/layout.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReferenceDataLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout */ \"(ssr)/./src/components/layout/index.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Database_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Database!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n/**\n * Reference Data Layout Component\n *\n * This component provides a consistent layout for all reference data-related pages\n * using the standardized DashboardLayout component with collapsible sidebar.\n */ function ReferenceDataLayout({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Determine the current page title based on the pathname\n    let pageTitle = 'Reference Data';\n    let categoryTitle = '';\n    if (pathname !== '/reference-data') {\n        const type = pathname.split('/').pop();\n        // Define category titles\n        const categoryInfo = {\n            territories: 'Territories',\n            segments: 'Segments',\n            competitors: 'Competitors',\n            serviceVisitType: 'Service Visit Types',\n            complaintType: 'Complaint Types',\n            complaintNatureType: 'Complaint Nature Types',\n            failureType: 'Failure Types',\n            spareType: 'Spare Types',\n            measurementType: 'Measurement Types',\n            priorityTypes: 'Priority Types',\n            priorityType: 'Priority Types',\n            enquiryTypes: 'Enquiry Types',\n            enquiryType: 'Enquiry Types',\n            deductionTypes: 'Deduction Types',\n            deductionType: 'Deduction Types',\n            debitDivisions: 'Debit Divisions',\n            debitDivision: 'Debit Divisions',\n            accountDivisions: 'Account Divisions',\n            accountDivision: 'Account Divisions',\n            spareParts: 'Spare Parts',\n            sparePart: 'Spare Parts',\n            taxRates: 'Tax Rates',\n            taxRate: 'Tax Rates',\n            transitDamageTypes: 'Transit Damage Types',\n            transitDamageType: 'Transit Damage Types',\n            userGroups: 'User Groups',\n            userGroup: 'User Groups',\n            uspTypes: 'USP Types',\n            uspType: 'USP Types',\n            visitTypes: 'Visit Types',\n            visitType: 'Visit Types',\n            divisions: 'Divisions',\n            brands: 'Brands'\n        };\n        categoryTitle = categoryInfo[type] || type || '';\n        pageTitle = categoryTitle;\n    }\n    // Define breadcrumbs for the page\n    const breadcrumbs = [\n        {\n            label: 'Dashboard',\n            href: '/dashboard'\n        },\n        {\n            label: 'Reference Data',\n            href: '/reference-data',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Database_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\layout.tsx\",\n                lineNumber: 70,\n                columnNumber: 11\n            }, this)\n        }\n    ];\n    // Add additional breadcrumb for subpages\n    if (pathname !== '/reference-data' && categoryTitle) {\n        breadcrumbs.push({\n            label: categoryTitle,\n            href: pathname,\n            current: true\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_1__.DashboardLayout, {\n        title: pageTitle,\n        requireAuth: true,\n        allowedRoles: [\n            'ADMIN',\n            'MANAGER'\n        ],\n        breadcrumbs: breadcrumbs,\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\layout.tsx\",\n        lineNumber: 81,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/reference-data/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/reference-data/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/reference-data/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReferenceDataPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./src/components/ui/use-toast.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ruler.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bookmark,ChevronRight,Database,FileText,Layers,MapPin,RefreshCw,Ruler,Tag,Users,Wrench!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n// Define reference data categories with icons and descriptions\nconst referenceCategories = [\n    {\n        id: 'territories',\n        name: 'Territories',\n        description: 'Geographic regions for business operations',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 17,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-blue-100 text-blue-600'\n    },\n    {\n        id: 'segments',\n        name: 'Segments',\n        description: 'Customer segmentation categories',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 23,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-green-100 text-green-600'\n    },\n    {\n        id: 'competitors',\n        name: 'Competitors',\n        description: 'Competing businesses in the market',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-purple-100 text-purple-600'\n    },\n    {\n        id: 'serviceVisitType',\n        name: 'Service Visit Types',\n        description: 'Types of service visits conducted',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 35,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-orange-100 text-orange-600'\n    },\n    {\n        id: 'complaintType',\n        name: 'Complaint Types',\n        description: 'Categories of customer complaints',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 41,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-red-100 text-red-600'\n    },\n    {\n        id: 'complaintNatureType',\n        name: 'Complaint Nature Types',\n        description: 'Nature or characteristics of complaints',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 47,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-yellow-100 text-yellow-600'\n    },\n    {\n        id: 'failureType',\n        name: 'Failure Types',\n        description: 'Categories of equipment failures',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 53,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-indigo-100 text-indigo-600'\n    },\n    {\n        id: 'spareType',\n        name: 'Spare Types',\n        description: 'Categories of spare parts',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-teal-100 text-teal-600'\n    },\n    {\n        id: 'measurementType',\n        name: 'Measurement Types',\n        description: 'Types of measurements used',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 65,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-pink-100 text-pink-600'\n    },\n    {\n        id: 'priorityTypes',\n        name: 'Priority Types',\n        description: 'Levels of priority for tasks and issues',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 71,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-red-100 text-red-600'\n    },\n    {\n        id: 'enquiryTypes',\n        name: 'Enquiry Types',\n        description: 'Categories of customer enquiries',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 77,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-yellow-100 text-yellow-600'\n    },\n    {\n        id: 'deductionTypes',\n        name: 'Deduction Types',\n        description: 'Types of deductions applied',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 83,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-purple-100 text-purple-600'\n    },\n    {\n        id: 'debitDivisions',\n        name: 'Debit Divisions',\n        description: 'Divisions for debit categorization',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 89,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-gray-100 text-gray-600'\n    },\n    {\n        id: 'accountDivisions',\n        name: 'Account Divisions',\n        description: 'Divisions for account categorization',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 95,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-blue-100 text-blue-600'\n    },\n    {\n        id: 'spareParts',\n        name: 'Spare Parts',\n        description: 'Replacement parts for equipment',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 101,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-teal-100 text-teal-600'\n    },\n    {\n        id: 'taxRates',\n        name: 'Tax Rates',\n        description: 'Different tax rates applied to products',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 107,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-green-100 text-green-600'\n    },\n    {\n        id: 'divisions',\n        name: 'Divisions',\n        description: 'Business divisions or departments',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 113,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-gray-100 text-gray-600'\n    },\n    {\n        id: 'brands',\n        name: 'Brands',\n        description: 'Product brands and manufacturers',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-6 w-6\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 119,\n            columnNumber: 9\n        }, undefined),\n        color: 'bg-blue-100 text-blue-600'\n    }\n];\n/**\n * Reference Data Management Page\n *\n * This page displays all reference data categories and allows users to navigate to specific category pages.\n */ function ReferenceDataPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [categoryCounts, setCategoryCounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch category counts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReferenceDataPage.useEffect\": ()=>{\n            const fetchCategoryCounts = {\n                \"ReferenceDataPage.useEffect.fetchCategoryCounts\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const counts = {};\n                        // Fetch counts for each category\n                        for (const category of referenceCategories){\n                            try {\n                                const response = await fetch(`/api/reference/${category.id}?take=1`, {\n                                    credentials: 'include'\n                                });\n                                if (response.ok) {\n                                    const data = await response.json();\n                                    counts[category.id] = data.meta?.total || 0;\n                                }\n                            } catch (categoryError) {\n                                console.error(`Error fetching count for ${category.name}:`, categoryError);\n                            }\n                        }\n                        setCategoryCounts(counts);\n                    } catch (error) {\n                        console.error('Error fetching reference data counts:', error);\n                        setError(error.message || 'Failed to load reference data counts');\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                            title: 'Error',\n                            description: 'Failed to load reference data counts. Please try again.',\n                            variant: 'destructive'\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ReferenceDataPage.useEffect.fetchCategoryCounts\"];\n            fetchCategoryCounts();\n        }\n    }[\"ReferenceDataPage.useEffect\"], []);\n    // Handle refresh\n    const handleRefresh = ()=>{\n        setIsLoading(true);\n        setError(null);\n        const fetchCategoryCounts = async ()=>{\n            try {\n                const counts = {};\n                // Fetch counts for each category\n                for (const category of referenceCategories){\n                    try {\n                        const response = await fetch(`/api/reference/${category.id}?take=1`, {\n                            credentials: 'include'\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            counts[category.id] = data.meta?.total || 0;\n                        }\n                    } catch (categoryError) {\n                        console.error(`Error fetching count for ${category.name}:`, categoryError);\n                    }\n                }\n                setCategoryCounts(counts);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: 'Success',\n                    description: 'Reference data counts refreshed successfully'\n                });\n            } catch (error) {\n                console.error('Error refreshing reference data counts:', error);\n                setError(error.message || 'Failed to refresh reference data counts');\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.toast)({\n                    title: 'Error',\n                    description: 'Failed to refresh reference data counts. Please try again.',\n                    variant: 'destructive'\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchCategoryCounts();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    className: \"pb-3 bg-primary text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Reference Data Management\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        className: \"text-gray-100\",\n                                        children: \"Manage reference data categories used throughout the application\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"secondary\",\n                                onClick: handleRefresh,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"pt-6\",\n                    children: error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative\",\n                        role: \"alert\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold\",\n                                children: \"Error: \"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block sm:inline\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 20\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: referenceCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: `flex flex-row items-center gap-3 ${category.color} p-4`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"rounded-full bg-white p-2\",\n                                                children: category.icon\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                        className: \"text-gray-700\",\n                                                        children: category.description\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                                            className: \"h-4 w-16\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 40\n                                                        }, this) : `${categoryCounts[category.id] || 0} items`\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                        className: \"p-4 pt-0 flex justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            asChild: true,\n                                            variant: \"default\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: `/reference-data/${category.id}`,\n                                                children: [\n                                                    \"Manage\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bookmark_ChevronRight_Database_FileText_Layers_MapPin_RefreshCw_Ruler_Tag_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, category.id, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 52\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 22\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\reference-data\\\\page.tsx\",\n        lineNumber: 213,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3JlZmVyZW5jZS1kYXRhL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDQTtBQUNmO0FBUUM7QUFRa0I7QUFDSTtBQUNGO0FBZTVCO0FBRXJCO0FBQ0EsTUFBTXlCLG1CQUFtQixHQUFHO0lBQzFCO1FBQ0VDLEVBQUUsRUFBRSxhQUFhO1FBQ2pCQyxJQUFJLEVBQUUsYUFBYTtRQUNuQkMsV0FBVyxFQUFFLDRDQUE0QztRQUN6REMsSUFBSSxnQkFBRSw4REFBQyxnTEFBTTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDakNDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUsVUFBVTtRQUNkQyxJQUFJLEVBQUUsVUFBVTtRQUNoQkMsV0FBVyxFQUFFLGtDQUFrQztRQUMvQ0MsSUFBSSxnQkFBRSw4REFBQyxnTEFBSztZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDaENDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUsYUFBYTtRQUNqQkMsSUFBSSxFQUFFLGFBQWE7UUFDbkJDLFdBQVcsRUFBRSxvQ0FBb0M7UUFDakRDLElBQUksZ0JBQUUsOERBQUMsaUxBQUc7WUFBQyxTQUFTLEVBQUMsU0FBUzs7Ozs7O1FBQzlCQyxLQUFLLEVBQUU7SUFDVCxDQUFDO0lBQ0Q7UUFDRUosRUFBRSxFQUFFLGtCQUFrQjtRQUN0QkMsSUFBSSxFQUFFLHFCQUFxQjtRQUMzQkMsV0FBVyxFQUFFLG1DQUFtQztRQUNoREMsSUFBSSxnQkFBRSw4REFBQyxpTEFBTTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDakNDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUsZUFBZTtRQUNuQkMsSUFBSSxFQUFFLGlCQUFpQjtRQUN2QkMsV0FBVyxFQUFFLG1DQUFtQztRQUNoREMsSUFBSSxnQkFBRSw4REFBQyxpTEFBYTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDeENDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUscUJBQXFCO1FBQ3pCQyxJQUFJLEVBQUUsd0JBQXdCO1FBQzlCQyxXQUFXLEVBQUUseUNBQXlDO1FBQ3REQyxJQUFJLGdCQUFFLDhEQUFDLGlMQUFRO1lBQUMsU0FBUyxFQUFDLFNBQVM7Ozs7OztRQUNuQ0MsS0FBSyxFQUFFO0lBQ1QsQ0FBQztJQUNEO1FBQ0VKLEVBQUUsRUFBRSxhQUFhO1FBQ2pCQyxJQUFJLEVBQUUsZUFBZTtRQUNyQkMsV0FBVyxFQUFFLGtDQUFrQztRQUMvQ0MsSUFBSSxnQkFBRSw4REFBQyxpTEFBTTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDakNDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUsV0FBVztRQUNmQyxJQUFJLEVBQUUsYUFBYTtRQUNuQkMsV0FBVyxFQUFFLDJCQUEyQjtRQUN4Q0MsSUFBSSxnQkFBRSw4REFBQyxpTEFBTTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDakNDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUsaUJBQWlCO1FBQ3JCQyxJQUFJLEVBQUUsbUJBQW1CO1FBQ3pCQyxXQUFXLEVBQUUsNEJBQTRCO1FBQ3pDQyxJQUFJLGdCQUFFLDhEQUFDLGlMQUFLO1lBQUMsU0FBUyxFQUFDLFNBQVM7Ozs7OztRQUNoQ0MsS0FBSyxFQUFFO0lBQ1QsQ0FBQztJQUNEO1FBQ0VKLEVBQUUsRUFBRSxlQUFlO1FBQ25CQyxJQUFJLEVBQUUsZ0JBQWdCO1FBQ3RCQyxXQUFXLEVBQUUseUNBQXlDO1FBQ3REQyxJQUFJLGdCQUFFLDhEQUFDLGlMQUFhO1lBQUMsU0FBUyxFQUFDLFNBQVM7Ozs7OztRQUN4Q0MsS0FBSyxFQUFFO0lBQ1QsQ0FBQztJQUNEO1FBQ0VKLEVBQUUsRUFBRSxjQUFjO1FBQ2xCQyxJQUFJLEVBQUUsZUFBZTtRQUNyQkMsV0FBVyxFQUFFLGtDQUFrQztRQUMvQ0MsSUFBSSxnQkFBRSw4REFBQyxpTEFBUTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDbkNDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUsZ0JBQWdCO1FBQ3BCQyxJQUFJLEVBQUUsaUJBQWlCO1FBQ3ZCQyxXQUFXLEVBQUUsNkJBQTZCO1FBQzFDQyxJQUFJLGdCQUFFLDhEQUFDLGlMQUFHO1lBQUMsU0FBUyxFQUFDLFNBQVM7Ozs7OztRQUM5QkMsS0FBSyxFQUFFO0lBQ1QsQ0FBQztJQUNEO1FBQ0VKLEVBQUUsRUFBRSxnQkFBZ0I7UUFDcEJDLElBQUksRUFBRSxpQkFBaUI7UUFDdkJDLFdBQVcsRUFBRSxvQ0FBb0M7UUFDakRDLElBQUksZ0JBQUUsOERBQUMsaUxBQU07WUFBQyxTQUFTLEVBQUMsU0FBUzs7Ozs7O1FBQ2pDQyxLQUFLLEVBQUU7SUFDVCxDQUFDO0lBQ0Q7UUFDRUosRUFBRSxFQUFFLGtCQUFrQjtRQUN0QkMsSUFBSSxFQUFFLG1CQUFtQjtRQUN6QkMsV0FBVyxFQUFFLHNDQUFzQztRQUNuREMsSUFBSSxnQkFBRSw4REFBQyxpTEFBTTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDakNDLEtBQUssRUFBRTtJQUNULENBQUM7SUFDRDtRQUNFSixFQUFFLEVBQUUsWUFBWTtRQUNoQkMsSUFBSSxFQUFFLGFBQWE7UUFDbkJDLFdBQVcsRUFBRSxpQ0FBaUM7UUFDOUNDLElBQUksZ0JBQUUsOERBQUMsaUxBQU07WUFBQyxTQUFTLEVBQUMsU0FBUzs7Ozs7O1FBQ2pDQyxLQUFLLEVBQUU7SUFDVCxDQUFDO0lBQ0Q7UUFDRUosRUFBRSxFQUFFLFVBQVU7UUFDZEMsSUFBSSxFQUFFLFdBQVc7UUFDakJDLFdBQVcsRUFBRSx5Q0FBeUM7UUFDdERDLElBQUksZ0JBQUUsOERBQUMsaUxBQUc7WUFBQyxTQUFTLEVBQUMsU0FBUzs7Ozs7O1FBQzlCQyxLQUFLLEVBQUU7SUFDVCxDQUFDO0lBQ0Q7UUFDRUosRUFBRSxFQUFFLFdBQVc7UUFDZkMsSUFBSSxFQUFFLFdBQVc7UUFDakJDLFdBQVcsRUFBRSxtQ0FBbUM7UUFDaERDLElBQUksZ0JBQUUsOERBQUMsaUxBQU07WUFBQyxTQUFTLEVBQUMsU0FBUzs7Ozs7O1FBQ2pDQyxLQUFLLEVBQUU7SUFDVCxDQUFDO0lBQ0Q7UUFDRUosRUFBRSxFQUFFLFFBQVE7UUFDWkMsSUFBSSxFQUFFLFFBQVE7UUFDZEMsV0FBVyxFQUFFLGtDQUFrQztRQUMvQ0MsSUFBSSxnQkFBRSw4REFBQyxpTEFBUTtZQUFDLFNBQVMsRUFBQyxTQUFTOzs7Ozs7UUFDbkNDLEtBQUssRUFBRTtJQUNULENBQUM7Q0FDRjtBQUVEOzs7O0NBSUEsR0FDZSxTQUFTQyxpQkFBaUJBLENBQUEsRUFBRztJQUMxQyxNQUFNQyxNQUFNLEdBQUc5QiwwREFBUyxDQUFDLENBQUM7SUFDMUIsTUFBTSxDQUFDK0IsU0FBUyxFQUFFQyxZQUFZLENBQUMsR0FBR2xDLCtDQUFRLENBQUMsSUFBSSxDQUFDO0lBQ2hELE1BQU0sQ0FBQ21DLEtBQUssRUFBRUMsUUFBUSxDQUFDLEdBQUdwQywrQ0FBUSxDQUFnQixJQUFJLENBQUM7SUFDdkQsTUFBTSxDQUFDcUMsY0FBYyxFQUFFQyxpQkFBaUIsQ0FBQyxHQUFHdEMsK0NBQVEsQ0FBeUIsQ0FBQyxDQUFDLENBQUM7SUFFaEY7SUFDQUMsZ0RBQVM7dUNBQUM7WUFDUixNQUFNc0MsbUJBQW1CO21FQUFHLE1BQUFBLENBQUE7b0JBQzFCLElBQUk7d0JBQ0ZMLFlBQVksQ0FBQyxJQUFJLENBQUM7d0JBQ2xCRSxRQUFRLENBQUMsSUFBSSxDQUFDO3dCQUVkLE1BQU1JLE1BQThCLEdBQUcsQ0FBQyxDQUFDO3dCQUV6Qzt3QkFDQSxLQUFLLE1BQU1DLFFBQVEsSUFBSWhCLG1CQUFtQixDQUFFOzRCQUMxQyxJQUFJO2dDQUNGLE1BQU1pQixRQUFRLEdBQUcsTUFBTUMsS0FBSyxDQUFFLGtCQUFpQkYsUUFBUSxDQUFDZixFQUFHLFNBQVEsRUFBRTtvQ0FDbkVrQixXQUFXLEVBQUU7Z0NBQ2YsQ0FBQyxDQUFDO2dDQUVGLElBQUlGLFFBQVEsQ0FBQ0csRUFBRSxFQUFFO29DQUNmLE1BQU1DLElBQUksR0FBRyxNQUFNSixRQUFRLENBQUNLLElBQUksQ0FBQyxDQUFDO29DQUNsQ1AsTUFBTSxDQUFDQyxRQUFRLENBQUNmLEVBQUUsQ0FBQyxHQUFHb0IsSUFBSSxDQUFDRSxJQUFJLEVBQUVDLEtBQUssSUFBSSxDQUFDO2dDQUM3Qzs0QkFDRixDQUFDLENBQUMsT0FBT0MsYUFBYSxFQUFFO2dDQUN0QkMsT0FBTyxDQUFDaEIsS0FBSyxDQUFFLDRCQUEyQk0sUUFBUSxDQUFDZCxJQUFLLEdBQUUsRUFBRXVCLGFBQWEsQ0FBQzs0QkFDNUU7d0JBQ0Y7d0JBRUFaLGlCQUFpQixDQUFDRSxNQUFNLENBQUM7b0JBQzNCLENBQUMsQ0FBQyxPQUFPTCxLQUFVLEVBQUU7d0JBQ25CZ0IsT0FBTyxDQUFDaEIsS0FBSyxDQUFDLHVDQUF1QyxFQUFFQSxLQUFLLENBQUM7d0JBQzdEQyxRQUFRLENBQUNELEtBQUssQ0FBQ2lCLE9BQU8sSUFBSSxzQ0FBc0MsQ0FBQzt3QkFDakV4QywrREFBSyxDQUFDOzRCQUNKeUMsS0FBSyxFQUFFLE9BQU87NEJBQ2R6QixXQUFXLEVBQUUseURBQXlEOzRCQUN0RTBCLE9BQU8sRUFBRTt3QkFDWCxDQUFDLENBQUM7b0JBQ0osQ0FBQyxRQUFTO3dCQUNScEIsWUFBWSxDQUFDLEtBQUssQ0FBQztvQkFDckI7Z0JBQ0YsQ0FBQzs7WUFFREssbUJBQW1CLENBQUMsQ0FBQztRQUN2QixDQUFDO3NDQUFFLEVBQUUsQ0FBQztJQUVOO0lBQ0EsTUFBTWdCLGFBQWEsR0FBR0EsQ0FBQTtRQUNwQnJCLFlBQVksQ0FBQyxJQUFJLENBQUM7UUFDbEJFLFFBQVEsQ0FBQyxJQUFJLENBQUM7UUFFZCxNQUFNRyxtQkFBbUIsR0FBRyxNQUFBQSxDQUFBO1lBQzFCLElBQUk7Z0JBQ0YsTUFBTUMsTUFBOEIsR0FBRyxDQUFDLENBQUM7Z0JBRXpDO2dCQUNBLEtBQUssTUFBTUMsUUFBUSxJQUFJaEIsbUJBQW1CLENBQUU7b0JBQzFDLElBQUk7d0JBQ0YsTUFBTWlCLFFBQVEsR0FBRyxNQUFNQyxLQUFLLENBQUUsa0JBQWlCRixRQUFRLENBQUNmLEVBQUcsU0FBUSxFQUFFOzRCQUNuRWtCLFdBQVcsRUFBRTt3QkFDZixDQUFDLENBQUM7d0JBRUYsSUFBSUYsUUFBUSxDQUFDRyxFQUFFLEVBQUU7NEJBQ2YsTUFBTUMsSUFBSSxHQUFHLE1BQU1KLFFBQVEsQ0FBQ0ssSUFBSSxDQUFDLENBQUM7NEJBQ2xDUCxNQUFNLENBQUNDLFFBQVEsQ0FBQ2YsRUFBRSxDQUFDLEdBQUdvQixJQUFJLENBQUNFLElBQUksRUFBRUMsS0FBSyxJQUFJLENBQUM7d0JBQzdDO29CQUNGLENBQUMsQ0FBQyxPQUFPQyxhQUFhLEVBQUU7d0JBQ3RCQyxPQUFPLENBQUNoQixLQUFLLENBQUUsNEJBQTJCTSxRQUFRLENBQUNkLElBQUssR0FBRSxFQUFFdUIsYUFBYSxDQUFDO29CQUM1RTtnQkFDRjtnQkFFQVosaUJBQWlCLENBQUNFLE1BQU0sQ0FBQztnQkFDekI1QiwrREFBSyxDQUFDO29CQUNKeUMsS0FBSyxFQUFFLFNBQVM7b0JBQ2hCekIsV0FBVyxFQUFFO2dCQUNmLENBQUMsQ0FBQztZQUNKLENBQUMsQ0FBQyxPQUFPTyxLQUFVLEVBQUU7Z0JBQ25CZ0IsT0FBTyxDQUFDaEIsS0FBSyxDQUFDLHlDQUF5QyxFQUFFQSxLQUFLLENBQUM7Z0JBQy9EQyxRQUFRLENBQUNELEtBQUssQ0FBQ2lCLE9BQU8sSUFBSSx5Q0FBeUMsQ0FBQztnQkFDcEV4QywrREFBSyxDQUFDO29CQUNKeUMsS0FBSyxFQUFFLE9BQU87b0JBQ2R6QixXQUFXLEVBQUUsNERBQTREO29CQUN6RTBCLE9BQU8sRUFBRTtnQkFDWCxDQUFDLENBQUM7WUFDSixDQUFDLFFBQVM7Z0JBQ1JwQixZQUFZLENBQUMsS0FBSyxDQUFDO1lBQ3JCO1FBQ0YsQ0FBQztRQUVESyxtQkFBbUIsQ0FBQyxDQUFDO0lBQ3ZCLENBQUM7SUFFRCxxQkFDRSw4REFBQyxHQUFHO1FBQUMsU0FBUyxFQUFDLFdBQVc7Z0NBRXhCLDhEQUFDLHFEQUFJOzs4QkFDSCw4REFBQywyREFBVTtvQkFBQyxTQUFTLEVBQUMsNEJBQTRCOzRDQUNoRCw4REFBQyxHQUFHO3dCQUFDLFNBQVMsRUFBQyw0Q0FBNEM7OzBDQUN6RCw4REFBQyxHQUFHOztrREFDRiw4REFBQywwREFBUztrREFBQyx5QkFBeUIsRUFBRTs7Ozs7O2tEQUN0Qyw4REFBQyxnRUFBZTt3Q0FBQyxTQUFTLEVBQUMsZUFBZTtrREFBQTs7Ozs7Ozs7Ozs7OzBDQUk1Qyw4REFBQyx5REFBTTtnQ0FBQyxPQUFPLEVBQUMsV0FBVztnQ0FBQyxPQUFPLENBQUMsQ0FBQ2dCLGFBQWEsQ0FBQztnQ0FBQyxRQUFRLENBQUMsQ0FBQ3RCLFNBQVMsQ0FBQzs7a0RBQ3RFLDhEQUFDLGlMQUFTO3dDQUFDLFNBQVMsRUFBQyxjQUFjOzs7Ozs7b0NBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLekMsOERBQUMsNERBQVc7b0JBQUMsU0FBUyxFQUFDLE1BQU07OEJBQzFCRSxLQUFLLGlCQUNKLDhEQUFDLEdBQUc7d0JBQUMsU0FBUyxFQUFDLHlFQUF5RTt3QkFBQyxJQUFJLEVBQUMsT0FBTzs7MENBQ25HLDhEQUFDLE1BQU07Z0NBQUMsU0FBUyxFQUFDLFdBQVc7MENBQUMsT0FBTyxFQUFFOzs7Ozs7MENBQ3ZDLDhEQUFDLElBQUk7Z0NBQUMsU0FBUyxFQUFDLGlCQUFpQixDQUFDOzBDQUFDQSxLQUFLOzs7Ozs7Ozs7Ozs2Q0FHMUMsOERBQUMsR0FBRzt3QkFBQyxTQUFTLEVBQUMsc0RBQXNEO2tDQUNsRVYsbUJBQW1CLENBQUMrQixHQUFHLENBQUVmLFFBQVEsa0JBQ2hDLDhEQUFDLHFEQUFJLENBQUM7Z0NBQWtCLFNBQVMsRUFBQyxpQkFBaUI7O2tEQUNqRCw4REFBQywyREFBVTt3Q0FBQyxTQUFTLENBQUMsQ0FBRSxvQ0FBbUNBLFFBQVEsQ0FBQ1gsS0FBTSxNQUFLLENBQUM7OzBEQUM5RSw4REFBQyxHQUFHO2dEQUFDLFNBQVMsRUFBQywyQkFBMkI7MERBQ3ZDVyxRQUFRLENBQUNaLElBQUk7Ozs7OzswREFFaEIsOERBQUMsR0FBRzs7a0VBQ0YsOERBQUMsMERBQVM7d0RBQUMsU0FBUyxFQUFDLFNBQVMsQ0FBQztrRUFBQ1ksUUFBUSxDQUFDZCxJQUFJOzs7Ozs7a0VBQzdDLDhEQUFDLGdFQUFlO3dEQUFDLFNBQVMsRUFBQyxlQUFlO2tFQUN2Q2MsUUFBUSxDQUFDYixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTNCLDhEQUFDLDREQUFXO3dDQUFDLFNBQVMsRUFBQyxLQUFLO2dFQUMxQiw4REFBQyxHQUFHOzRDQUFDLFNBQVMsRUFBQyxtQ0FBbUM7b0VBQ2hELDhEQUFDLEdBQUc7Z0RBQUMsU0FBUyxFQUFDLG1CQUFtQjs7a0VBQ2hDLDhEQUFDLGlMQUFRO3dEQUFDLFNBQVMsRUFBQyw0QkFBNEI7Ozs7OztrRUFDaEQsOERBQUMsSUFBSTtrRUFDRkssU0FBUyxpQkFDUiw4REFBQyw2REFBUTs0REFBQyxTQUFTLEVBQUMsVUFBVTs7Ozs7bUVBRTdCLEdBQUVJLGNBQWMsQ0FBQ0ksUUFBUSxDQUFDZixFQUFFLENBQUMsSUFBSSxDQUFFLFFBQ3JDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUtULDhEQUFDLDJEQUFVO3dDQUFDLFNBQVMsRUFBQywyQkFBMkI7a0RBQy9DLDRFQUFDLHlEQUFNOzRDQUFDLE9BQU87NENBQUMsT0FBTyxFQUFDLFNBQVM7b0VBQy9CLDhEQUFDLGtEQUFJO2dEQUFDLElBQUksQ0FBQyxDQUFFLG1CQUFrQmUsUUFBUSxDQUFDZixFQUFHLEVBQUMsQ0FBQzs7b0RBQUE7a0VBRTNDLDhEQUFDLGlMQUFZO3dEQUFDLFNBQVMsRUFBQyxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkE5Qm5DZSxRQUFRLENBQUNmLEVBQUUsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUEwQ3ZDIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXHNyY1xcYXBwXFxyZWZlcmVuY2UtZGF0YVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQge1xuICBDYXJkLFxuICBDYXJkQ29udGVudCxcbiAgQ2FyZERlc2NyaXB0aW9uLFxuICBDYXJkSGVhZGVyLFxuICBDYXJkVGl0bGUsXG4gIENhcmRGb290ZXJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHtcbiAgQnJlYWRjcnVtYixcbiAgQnJlYWRjcnVtYkl0ZW0sXG4gIEJyZWFkY3J1bWJMaW5rLFxuICBCcmVhZGNydW1iTGlzdCxcbiAgQnJlYWRjcnVtYlNlcGFyYXRvclxufSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnJlYWRjcnVtYic7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IFNrZWxldG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NrZWxldG9uJztcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3VzZS10b2FzdCc7XG5pbXBvcnQge1xuICBIb21lLFxuICBEYXRhYmFzZSxcbiAgUmVmcmVzaEN3LFxuICBDaGV2cm9uUmlnaHQsXG4gIFRhZyxcbiAgTWFwUGluLFxuICBVc2VycyxcbiAgRmlsZVRleHQsXG4gIEFsZXJ0VHJpYW5nbGUsXG4gIFdyZW5jaCxcbiAgUnVsZXIsXG4gIExheWVycyxcbiAgQm9va21hcmtcbn0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuLy8gRGVmaW5lIHJlZmVyZW5jZSBkYXRhIGNhdGVnb3JpZXMgd2l0aCBpY29ucyBhbmQgZGVzY3JpcHRpb25zXG5jb25zdCByZWZlcmVuY2VDYXRlZ29yaWVzID0gW1xuICB7XG4gICAgaWQ6ICd0ZXJyaXRvcmllcycsXG4gICAgbmFtZTogJ1RlcnJpdG9yaWVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0dlb2dyYXBoaWMgcmVnaW9ucyBmb3IgYnVzaW5lc3Mgb3BlcmF0aW9ucycsXG4gICAgaWNvbjogPE1hcFBpbiBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgY29sb3I6ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNjAwJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdzZWdtZW50cycsXG4gICAgbmFtZTogJ1NlZ21lbnRzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0N1c3RvbWVyIHNlZ21lbnRhdGlvbiBjYXRlZ29yaWVzJyxcbiAgICBpY29uOiA8VXNlcnMgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+LFxuICAgIGNvbG9yOiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tNjAwJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdjb21wZXRpdG9ycycsXG4gICAgbmFtZTogJ0NvbXBldGl0b3JzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0NvbXBldGluZyBidXNpbmVzc2VzIGluIHRoZSBtYXJrZXQnLFxuICAgIGljb246IDxUYWcgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+LFxuICAgIGNvbG9yOiAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS02MDAnXG4gIH0sXG4gIHtcbiAgICBpZDogJ3NlcnZpY2VWaXNpdFR5cGUnLFxuICAgIG5hbWU6ICdTZXJ2aWNlIFZpc2l0IFR5cGVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1R5cGVzIG9mIHNlcnZpY2UgdmlzaXRzIGNvbmR1Y3RlZCcsXG4gICAgaWNvbjogPFdyZW5jaCBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgY29sb3I6ICdiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTYwMCdcbiAgfSxcbiAge1xuICAgIGlkOiAnY29tcGxhaW50VHlwZScsXG4gICAgbmFtZTogJ0NvbXBsYWludCBUeXBlcycsXG4gICAgZGVzY3JpcHRpb246ICdDYXRlZ29yaWVzIG9mIGN1c3RvbWVyIGNvbXBsYWludHMnLFxuICAgIGljb246IDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICBjb2xvcjogJ2JnLXJlZC0xMDAgdGV4dC1yZWQtNjAwJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdjb21wbGFpbnROYXR1cmVUeXBlJyxcbiAgICBuYW1lOiAnQ29tcGxhaW50IE5hdHVyZSBUeXBlcycsXG4gICAgZGVzY3JpcHRpb246ICdOYXR1cmUgb3IgY2hhcmFjdGVyaXN0aWNzIG9mIGNvbXBsYWludHMnLFxuICAgIGljb246IDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgY29sb3I6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTYwMCdcbiAgfSxcbiAge1xuICAgIGlkOiAnZmFpbHVyZVR5cGUnLFxuICAgIG5hbWU6ICdGYWlsdXJlIFR5cGVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0NhdGVnb3JpZXMgb2YgZXF1aXBtZW50IGZhaWx1cmVzJyxcbiAgICBpY29uOiA8V3JlbmNoIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICBjb2xvcjogJ2JnLWluZGlnby0xMDAgdGV4dC1pbmRpZ28tNjAwJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdzcGFyZVR5cGUnLFxuICAgIG5hbWU6ICdTcGFyZSBUeXBlcycsXG4gICAgZGVzY3JpcHRpb246ICdDYXRlZ29yaWVzIG9mIHNwYXJlIHBhcnRzJyxcbiAgICBpY29uOiA8V3JlbmNoIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICBjb2xvcjogJ2JnLXRlYWwtMTAwIHRleHQtdGVhbC02MDAnXG4gIH0sXG4gIHtcbiAgICBpZDogJ21lYXN1cmVtZW50VHlwZScsXG4gICAgbmFtZTogJ01lYXN1cmVtZW50IFR5cGVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1R5cGVzIG9mIG1lYXN1cmVtZW50cyB1c2VkJyxcbiAgICBpY29uOiA8UnVsZXIgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+LFxuICAgIGNvbG9yOiAnYmctcGluay0xMDAgdGV4dC1waW5rLTYwMCdcbiAgfSxcbiAge1xuICAgIGlkOiAncHJpb3JpdHlUeXBlcycsXG4gICAgbmFtZTogJ1ByaW9yaXR5IFR5cGVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0xldmVscyBvZiBwcmlvcml0eSBmb3IgdGFza3MgYW5kIGlzc3VlcycsXG4gICAgaWNvbjogPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+LFxuICAgIGNvbG9yOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC02MDAnXG4gIH0sXG4gIHtcbiAgICBpZDogJ2VucXVpcnlUeXBlcycsXG4gICAgbmFtZTogJ0VucXVpcnkgVHlwZXMnLFxuICAgIGRlc2NyaXB0aW9uOiAnQ2F0ZWdvcmllcyBvZiBjdXN0b21lciBlbnF1aXJpZXMnLFxuICAgIGljb246IDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgY29sb3I6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTYwMCdcbiAgfSxcbiAge1xuICAgIGlkOiAnZGVkdWN0aW9uVHlwZXMnLFxuICAgIG5hbWU6ICdEZWR1Y3Rpb24gVHlwZXMnLFxuICAgIGRlc2NyaXB0aW9uOiAnVHlwZXMgb2YgZGVkdWN0aW9ucyBhcHBsaWVkJyxcbiAgICBpY29uOiA8VGFnIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICBjb2xvcjogJ2JnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtNjAwJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdkZWJpdERpdmlzaW9ucycsXG4gICAgbmFtZTogJ0RlYml0IERpdmlzaW9ucycsXG4gICAgZGVzY3JpcHRpb246ICdEaXZpc2lvbnMgZm9yIGRlYml0IGNhdGVnb3JpemF0aW9uJyxcbiAgICBpY29uOiA8TGF5ZXJzIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICBjb2xvcjogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS02MDAnXG4gIH0sXG4gIHtcbiAgICBpZDogJ2FjY291bnREaXZpc2lvbnMnLFxuICAgIG5hbWU6ICdBY2NvdW50IERpdmlzaW9ucycsXG4gICAgZGVzY3JpcHRpb246ICdEaXZpc2lvbnMgZm9yIGFjY291bnQgY2F0ZWdvcml6YXRpb24nLFxuICAgIGljb246IDxMYXllcnMgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+LFxuICAgIGNvbG9yOiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTYwMCdcbiAgfSxcbiAge1xuICAgIGlkOiAnc3BhcmVQYXJ0cycsXG4gICAgbmFtZTogJ1NwYXJlIFBhcnRzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1JlcGxhY2VtZW50IHBhcnRzIGZvciBlcXVpcG1lbnQnLFxuICAgIGljb246IDxXcmVuY2ggY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+LFxuICAgIGNvbG9yOiAnYmctdGVhbC0xMDAgdGV4dC10ZWFsLTYwMCdcbiAgfSxcbiAge1xuICAgIGlkOiAndGF4UmF0ZXMnLFxuICAgIG5hbWU6ICdUYXggUmF0ZXMnLFxuICAgIGRlc2NyaXB0aW9uOiAnRGlmZmVyZW50IHRheCByYXRlcyBhcHBsaWVkIHRvIHByb2R1Y3RzJyxcbiAgICBpY29uOiA8VGFnIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICBjb2xvcjogJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTYwMCdcbiAgfSxcbiAge1xuICAgIGlkOiAnZGl2aXNpb25zJyxcbiAgICBuYW1lOiAnRGl2aXNpb25zJyxcbiAgICBkZXNjcmlwdGlvbjogJ0J1c2luZXNzIGRpdmlzaW9ucyBvciBkZXBhcnRtZW50cycsXG4gICAgaWNvbjogPExheWVycyBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgY29sb3I6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNjAwJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdicmFuZHMnLFxuICAgIG5hbWU6ICdCcmFuZHMnLFxuICAgIGRlc2NyaXB0aW9uOiAnUHJvZHVjdCBicmFuZHMgYW5kIG1hbnVmYWN0dXJlcnMnLFxuICAgIGljb246IDxCb29rbWFyayBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgY29sb3I6ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNjAwJ1xuICB9XG5dO1xuXG4vKipcbiAqIFJlZmVyZW5jZSBEYXRhIE1hbmFnZW1lbnQgUGFnZVxuICpcbiAqIFRoaXMgcGFnZSBkaXNwbGF5cyBhbGwgcmVmZXJlbmNlIGRhdGEgY2F0ZWdvcmllcyBhbmQgYWxsb3dzIHVzZXJzIHRvIG5hdmlnYXRlIHRvIHNwZWNpZmljIGNhdGVnb3J5IHBhZ2VzLlxuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSZWZlcmVuY2VEYXRhUGFnZSgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2NhdGVnb3J5Q291bnRzLCBzZXRDYXRlZ29yeUNvdW50c10gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBudW1iZXI+Pih7fSk7XG5cbiAgLy8gRmV0Y2ggY2F0ZWdvcnkgY291bnRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgZmV0Y2hDYXRlZ29yeUNvdW50cyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgICAgY29uc3QgY291bnRzOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+ID0ge307XG5cbiAgICAgICAgLy8gRmV0Y2ggY291bnRzIGZvciBlYWNoIGNhdGVnb3J5XG4gICAgICAgIGZvciAoY29uc3QgY2F0ZWdvcnkgb2YgcmVmZXJlbmNlQ2F0ZWdvcmllcykge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3JlZmVyZW5jZS8ke2NhdGVnb3J5LmlkfT90YWtlPTFgLCB7XG4gICAgICAgICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZSdcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgICAgY291bnRzW2NhdGVnb3J5LmlkXSA9IGRhdGEubWV0YT8udG90YWwgfHwgMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGNhdGNoIChjYXRlZ29yeUVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBmZXRjaGluZyBjb3VudCBmb3IgJHtjYXRlZ29yeS5uYW1lfTpgLCBjYXRlZ29yeUVycm9yKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBzZXRDYXRlZ29yeUNvdW50cyhjb3VudHMpO1xuICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyByZWZlcmVuY2UgZGF0YSBjb3VudHM6JywgZXJyb3IpO1xuICAgICAgICBzZXRFcnJvcihlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gbG9hZCByZWZlcmVuY2UgZGF0YSBjb3VudHMnKTtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAnRXJyb3InLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRmFpbGVkIHRvIGxvYWQgcmVmZXJlbmNlIGRhdGEgY291bnRzLiBQbGVhc2UgdHJ5IGFnYWluLicsXG4gICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgICB9KTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGZldGNoQ2F0ZWdvcnlDb3VudHMoKTtcbiAgfSwgW10pO1xuXG4gIC8vIEhhbmRsZSByZWZyZXNoXG4gIGNvbnN0IGhhbmRsZVJlZnJlc2ggPSAoKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgY29uc3QgZmV0Y2hDYXRlZ29yeUNvdW50cyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGNvdW50czogUmVjb3JkPHN0cmluZywgbnVtYmVyPiA9IHt9O1xuXG4gICAgICAgIC8vIEZldGNoIGNvdW50cyBmb3IgZWFjaCBjYXRlZ29yeVxuICAgICAgICBmb3IgKGNvbnN0IGNhdGVnb3J5IG9mIHJlZmVyZW5jZUNhdGVnb3JpZXMpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9yZWZlcmVuY2UvJHtjYXRlZ29yeS5pZH0/dGFrZT0xYCwge1xuICAgICAgICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnXG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgICAgIGNvdW50c1tjYXRlZ29yeS5pZF0gPSBkYXRhLm1ldGE/LnRvdGFsIHx8IDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBjYXRjaCAoY2F0ZWdvcnlFcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgZmV0Y2hpbmcgY291bnQgZm9yICR7Y2F0ZWdvcnkubmFtZX06YCwgY2F0ZWdvcnlFcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgc2V0Q2F0ZWdvcnlDb3VudHMoY291bnRzKTtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAnU3VjY2VzcycsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdSZWZlcmVuY2UgZGF0YSBjb3VudHMgcmVmcmVzaGVkIHN1Y2Nlc3NmdWxseSdcbiAgICAgICAgfSk7XG4gICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlZnJlc2hpbmcgcmVmZXJlbmNlIGRhdGEgY291bnRzOicsIGVycm9yKTtcbiAgICAgICAgc2V0RXJyb3IoZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIHJlZnJlc2ggcmVmZXJlbmNlIGRhdGEgY291bnRzJyk7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ0Vycm9yJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0ZhaWxlZCB0byByZWZyZXNoIHJlZmVyZW5jZSBkYXRhIGNvdW50cy4gUGxlYXNlIHRyeSBhZ2Fpbi4nLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgfSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBmZXRjaENhdGVnb3J5Q291bnRzKCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuXG4gICAgICA8Q2FyZD5cbiAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItMyBiZy1wcmltYXJ5IHRleHQtd2hpdGVcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZT5SZWZlcmVuY2UgRGF0YSBNYW5hZ2VtZW50PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICAgIE1hbmFnZSByZWZlcmVuY2UgZGF0YSBjYXRlZ29yaWVzIHVzZWQgdGhyb3VnaG91dCB0aGUgYXBwbGljYXRpb25cbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cInNlY29uZGFyeVwiIG9uQ2xpY2s9e2hhbmRsZVJlZnJlc2h9IGRpc2FibGVkPXtpc0xvYWRpbmd9PlxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIFJlZnJlc2hcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwdC02XCI+XG4gICAgICAgICAge2Vycm9yID8gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZCByZWxhdGl2ZVwiIHJvbGU9XCJhbGVydFwiPlxuICAgICAgICAgICAgICA8c3Ryb25nIGNsYXNzTmFtZT1cImZvbnQtYm9sZFwiPkVycm9yOiA8L3N0cm9uZz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgc206aW5saW5lXCI+e2Vycm9yfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAge3JlZmVyZW5jZUNhdGVnb3JpZXMubWFwKChjYXRlZ29yeSkgPT4gKFxuICAgICAgICAgICAgICAgIDxDYXJkIGtleT17Y2F0ZWdvcnkuaWR9IGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPXtgZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIgZ2FwLTMgJHtjYXRlZ29yeS5jb2xvcn0gcC00YH0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIGJnLXdoaXRlIHAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5pY29ufVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj57Y2F0ZWdvcnkubmFtZX08L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPERhdGFiYXNlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMiB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy0xNlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYCR7Y2F0ZWdvcnlDb3VudHNbY2F0ZWdvcnkuaWRdIHx8IDB9IGl0ZW1zYFxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8Q2FyZEZvb3RlciBjbGFzc05hbWU9XCJwLTQgcHQtMCBmbGV4IGp1c3RpZnktZW5kXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b24gYXNDaGlsZCB2YXJpYW50PVwiZGVmYXVsdFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2AvcmVmZXJlbmNlLWRhdGEvJHtjYXRlZ29yeS5pZH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIE1hbmFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L0NhcmRGb290ZXI+XG4gICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJMaW5rIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkNhcmRGb290ZXIiLCJCdXR0b24iLCJTa2VsZXRvbiIsInRvYXN0IiwiRGF0YWJhc2UiLCJSZWZyZXNoQ3ciLCJDaGV2cm9uUmlnaHQiLCJUYWciLCJNYXBQaW4iLCJVc2VycyIsIkZpbGVUZXh0IiwiQWxlcnRUcmlhbmdsZSIsIldyZW5jaCIsIlJ1bGVyIiwiTGF5ZXJzIiwiQm9va21hcmsiLCJyZWZlcmVuY2VDYXRlZ29yaWVzIiwiaWQiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJpY29uIiwiY29sb3IiLCJSZWZlcmVuY2VEYXRhUGFnZSIsInJvdXRlciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJjYXRlZ29yeUNvdW50cyIsInNldENhdGVnb3J5Q291bnRzIiwiZmV0Y2hDYXRlZ29yeUNvdW50cyIsImNvdW50cyIsImNhdGVnb3J5IiwicmVzcG9uc2UiLCJmZXRjaCIsImNyZWRlbnRpYWxzIiwib2siLCJkYXRhIiwianNvbiIsIm1ldGEiLCJ0b3RhbCIsImNhdGVnb3J5RXJyb3IiLCJjb25zb2xlIiwibWVzc2FnZSIsInRpdGxlIiwidmFyaWFudCIsImhhbmRsZVJlZnJlc2giLCJtYXAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/reference-data/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/admin-layout.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/admin-layout.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminLayout: () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _base_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./base-layout */ \"(ssr)/./src/components/layout/base-layout.tsx\");\n/* harmony import */ var _page_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./page-header */ \"(ssr)/./src/components/layout/page-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ChevronLeft,ChevronRight,Cog,Database,FileText,LayoutDashboard,Mail,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ AdminLayout auto */ \n\n\n\n\n\n\n\n\n// Interface for navigation items\n/**\n * AdminLayout Component\n *\n * A specialized layout for admin pages with a collapsible sidebar.\n */ function AdminLayout({ children, title, actions }) {\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Navigation items for the sidebar\n    const navItems = [\n        {\n            name: 'Dashboard',\n            href: '/admin',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 11\n            }, this),\n            exact: true\n        },\n        {\n            name: 'User Management',\n            href: '/admin/users',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'Activity Logs',\n            href: '/admin/activity-logs',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'Email Management',\n            href: '/admin/email',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'Templates',\n                    href: '/admin/email/templates'\n                },\n                {\n                    name: 'Preview',\n                    href: '/admin/email/preview'\n                },\n                {\n                    name: 'Create Template',\n                    href: '/admin/email/templates/create'\n                }\n            ]\n        },\n        {\n            name: 'System Settings',\n            href: '/admin/settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'Reference Data',\n            href: '/reference-data',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'Territories',\n                    href: '/reference-data/territories'\n                },\n                {\n                    name: 'Segments',\n                    href: '/reference-data/segments'\n                },\n                {\n                    name: 'Complaint Types',\n                    href: '/reference-data/complaintType'\n                },\n                {\n                    name: 'Service Visit Types',\n                    href: '/reference-data/serviceVisitType'\n                }\n            ]\n        },\n        {\n            name: 'Visit Cards',\n            href: '/visit-cards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                lineNumber: 79,\n                columnNumber: 11\n            }, this)\n        }\n    ];\n    // Check if a nav item is active\n    const isActive = (href, exact = false)=>{\n        if (exact) {\n            return pathname === href;\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base_layout__WEBPACK_IMPORTED_MODULE_4__.BaseLayout, {\n        requireAuth: true,\n        allowedRoles: [\n            'ADMIN'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"bg-white h-full shadow-md transition-all duration-300 flex flex-col\", sidebarCollapsed ? \"w-16\" : \"w-64\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 flex items-center justify-between border-b\",\n                            children: [\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: \"Admin\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                    \"aria-label\": sidebarCollapsed ? \"Expand Sidebar\" : \"Collapse Sidebar\",\n                                    className: \"border border-gray-200 bg-white shadow-sm\",\n                                    children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 74\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: item.children ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex items-center px-4 py-2 text-sm font-medium\", pathname.startsWith(item.href) ? \"bg-blue-50 text-primary\" : \"text-gray-700 hover:bg-gray-100\", sidebarCollapsed && \"justify-center\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: sidebarCollapsed ? \"\" : \"mr-3\",\n                                                            children: item.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 47\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 23\n                                                }, this),\n                                                !sidebarCollapsed && pathname.startsWith(item.href) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"mt-1 pl-8 space-y-1\",\n                                                    children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: child.href,\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"block px-4 py-2 text-sm font-medium\", pathname === child.href ? \"text-primary\" : \"text-gray-600 hover:text-primary\"),\n                                                                children: child.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, child.href, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 55\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 79\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 36\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex items-center px-4 py-2 text-sm font-medium\", isActive(item.href, item.exact) ? \"bg-blue-50 text-primary\" : \"text-gray-700 hover:bg-gray-100\", sidebarCollapsed && \"justify-center\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: sidebarCollapsed ? \"\" : \"mr-3\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, this),\n                                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 30\n                                        }, this)\n                                    }, item.href, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/dashboard\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex items-center text-sm font-medium text-gray-700 hover:text-primary\", sidebarCollapsed && \"justify-center\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ChevronLeft_ChevronRight_Cog_Database_FileText_LayoutDashboard_Mail_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-3\",\n                                        children: \"Back to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_page_header__WEBPACK_IMPORTED_MODULE_5__.PageHeader, {\n                            title: title,\n                            actions: actions,\n                            showDashboardLink: true,\n                            showAdminLink: false\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-y-auto p-6 bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\admin-layout.tsx\",\n        lineNumber: 89,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/admin-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/base-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/base-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseLayout: () => (/* binding */ BaseLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(ssr)/./src/lib/hooks/useAuth.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ BaseLayout auto */ \n\n\n\n\n/**\n * BaseLayout Component\n * \n * A foundation layout component that handles authentication and authorization.\n * It redirects unauthenticated users to the login page and checks role-based access.\n */ function BaseLayout({ children, requireAuth = true, allowedRoles = [] }) {\n    const { user, isLoading, isAuthenticated, hasRole } = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Redirect to login if authentication is required but user is not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BaseLayout.useEffect\": ()=>{\n            if (requireAuth && !isLoading && !isAuthenticated) {\n                const callbackUrl = encodeURIComponent(pathname);\n                router.push(`/auth/login?callbackUrl=${callbackUrl}`);\n            }\n        }\n    }[\"BaseLayout.useEffect\"], [\n        isLoading,\n        isAuthenticated,\n        pathname,\n        router,\n        requireAuth\n    ]);\n    // Check role-based access\n    const hasAccess = !requireAuth || isAuthenticated && (allowedRoles.length === 0 || allowedRoles.some((role)=>hasRole([\n            role\n        ])));\n    // If loading, show loading state\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-700\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n            lineNumber: 40,\n            columnNumber: 12\n        }, this);\n    }\n    // If authentication is required but user is not authenticated, show nothing (will be redirected by useEffect)\n    if (requireAuth && !isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold\",\n                        children: \"Authentication Required\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Please log in to access this page\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, this);\n    }\n    // If role-based access is required but user doesn't have the required role\n    if (requireAuth && isAuthenticated && !hasAccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"You don't have permission to access this page\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n            lineNumber: 60,\n            columnNumber: 12\n        }, this);\n    }\n    // Render children if all checks pass\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\base-layout.tsx\",\n        lineNumber: 69,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/base-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/dashboard-layout.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/dashboard-layout.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _base_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./base-layout */ \"(ssr)/./src/components/layout/base-layout.tsx\");\n/* harmony import */ var _page_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./page-header */ \"(ssr)/./src/components/layout/page-header.tsx\");\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(ssr)/./src/lib/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart4,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,FileText,LayoutDashboard,LogOut,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \n\n\n\n\n\n\n\n\n\n/**\n * DashboardLayout Component\n *\n * A layout with a collapsible sidebar navigation for the dashboard and related pages.\n */ function DashboardLayout({ children, title, actions, breadcrumbs, showAdminLink = false, requireAuth = true, allowedRoles = [] }) {\n    const { user, hasRole, logout } = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Navigation items for the sidebar\n    const navItems = [\n        {\n            name: 'Dashboard',\n            href: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'Customers',\n            href: '/customers',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'AMC Management',\n            href: '/amc',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'All Contracts',\n                    href: '/amc',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Payments',\n                    href: '/amc/payments',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Service Dates',\n                    href: '/amc/service-dates',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'New Contract',\n                    href: '/amc/new',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER',\n                        'EXECUTIVE'\n                    ]\n                }\n            ]\n        },\n        {\n            name: 'Warranty Management',\n            href: '/warranties',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 72,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'Overview',\n                    href: '/warranties',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'In-Warranty',\n                    href: '/warranties/in-warranty',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Out-of-Warranty',\n                    href: '/warranties/out-warranty',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Components',\n                    href: '/warranties/components',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Status Dashboard',\n                    href: '/warranties/status',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Alerts',\n                    href: '/warranties/alerts',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'BLUESTAR',\n                    href: '/warranties/bluestar',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: 'Reference Data',\n            href: '/reference-data',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 105,\n                columnNumber: 11\n            }, this),\n            roles: [\n                'ADMIN',\n                'MANAGER'\n            ]\n        },\n        {\n            name: 'Service',\n            href: '/service',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'Sales',\n            href: '/sales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 114,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'Reports',\n            href: '/reports',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 118,\n                columnNumber: 11\n            }, this),\n            roles: [\n                'ADMIN',\n                'MANAGER',\n                'EXECUTIVE'\n            ]\n        },\n        {\n            name: 'Admin',\n            href: '/admin',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, this),\n            roles: [\n                'ADMIN'\n            ]\n        }\n    ];\n    // Handle sign out\n    const handleSignOut = async ()=>{\n        await logout();\n        router.push('/auth/login');\n    };\n    // Toggle expanded state for menu items with children\n    const toggleExpanded = (href)=>{\n        setExpandedItems((prev)=>prev.includes(href) ? prev.filter((item)=>item !== href) : [\n                ...prev,\n                href\n            ]);\n    };\n    // Check if a nav item is active\n    const isActive = (href, children)=>{\n        if (pathname === href) return true;\n        if (children) {\n            return children.some((child)=>pathname === child.href || pathname.startsWith(`${child.href}/`));\n        }\n        return pathname.startsWith(`${href}/`);\n    };\n    // Check if a nav item should be expanded (active or manually expanded)\n    const isExpanded = (href, children)=>{\n        if (!children) return false;\n        return expandedItems.includes(href) || children.some((child)=>pathname === child.href || pathname.startsWith(`${child.href}/`));\n    };\n    // Default profile actions if none provided\n    const defaultActions = actions || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                asChild: true,\n                variant: \"outline\",\n                size: \"sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/profile\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        \"My Profile\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                variant: \"destructive\",\n                size: \"sm\",\n                onClick: handleSignOut,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    \"Sign Out\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 154,\n        columnNumber: 37\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base_layout__WEBPACK_IMPORTED_MODULE_4__.BaseLayout, {\n        requireAuth: requireAuth,\n        allowedRoles: allowedRoles,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"bg-white h-full shadow-md transition-all duration-300 flex flex-col\", sidebarCollapsed ? \"w-16\" : \"w-64\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 flex items-center justify-between border-b\",\n                            children: [\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: \"KoolSoft\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                    \"aria-label\": sidebarCollapsed ? \"Expand Sidebar\" : \"Collapse Sidebar\",\n                                    className: \"border border-gray-200 bg-white shadow-sm\",\n                                    children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 74\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1 px-2\",\n                                children: navItems.map((item)=>{\n                                    // Skip items that require specific roles if user doesn't have them\n                                    if (item.roles && !item.roles.some((role)=>hasRole([\n                                            role\n                                        ]))) {\n                                        return null;\n                                    }\n                                    const hasChildren = item.children && item.children.length > 0;\n                                    const itemIsActive = isActive(item.href, item.children);\n                                    const itemIsExpanded = isExpanded(item.href, item.children);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleExpanded(item.href),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"w-full flex items-center px-3 py-2 rounded-md transition-colors text-left\", itemIsActive ? 'bg-primary text-primary-foreground' : 'text-gray-700 hover:bg-secondary', sidebarCollapsed && \"justify-center\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: sidebarCollapsed ? \"\" : \"mr-2\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex-1\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart4_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_FileText_LayoutDashboard_LogOut_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"h-4 w-4 transition-transform\", itemIsExpanded && \"rotate-90\")\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 36\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"flex items-center px-3 py-2 rounded-md transition-colors\", itemIsActive ? 'bg-primary text-primary-foreground' : 'text-gray-700 hover:bg-secondary', sidebarCollapsed && \"justify-center\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: sidebarCollapsed ? \"\" : \"mr-2\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !sidebarCollapsed && item.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 35\n                                            }, this),\n                                            hasChildren && itemIsExpanded && !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 space-y-1\",\n                                                children: item.children.map((child)=>{\n                                                    // Skip child items that require specific roles if user doesn't have them\n                                                    if (child.roles && !child.roles.some((role)=>hasRole([\n                                                            role\n                                                        ]))) {\n                                                        return null;\n                                                    }\n                                                    const childIsActive = pathname === child.href || pathname.startsWith(`${child.href}/`);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"flex items-center px-3 py-2 rounded-md transition-colors text-sm\", childIsActive ? 'bg-primary/10 text-primary border-l-2 border-primary' : 'text-gray-600 hover:bg-secondary hover:text-gray-900'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: child.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            child.name\n                                                        ]\n                                                    }, child.href, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 28\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 76\n                                            }, this)\n                                        ]\n                                    }, item.href, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 22\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_page_header__WEBPACK_IMPORTED_MODULE_5__.PageHeader, {\n                            title: title,\n                            breadcrumbs: breadcrumbs,\n                            actions: defaultActions,\n                            showDashboardLink: false,\n                            showAdminLink: showAdminLink\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-y-auto p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 166,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/dashboard-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/index.ts":
/*!****************************************!*\
  !*** ./src/components/layout/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminLayout: () => (/* reexport safe */ _admin_layout__WEBPACK_IMPORTED_MODULE_2__.AdminLayout),\n/* harmony export */   BaseLayout: () => (/* reexport safe */ _base_layout__WEBPACK_IMPORTED_MODULE_0__.BaseLayout),\n/* harmony export */   DashboardLayout: () => (/* reexport safe */ _dashboard_layout__WEBPACK_IMPORTED_MODULE_1__.DashboardLayout),\n/* harmony export */   PageHeader: () => (/* reexport safe */ _page_header__WEBPACK_IMPORTED_MODULE_3__.PageHeader)\n/* harmony export */ });\n/* harmony import */ var _base_layout__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base-layout */ \"(ssr)/./src/components/layout/base-layout.tsx\");\n/* harmony import */ var _dashboard_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dashboard-layout */ \"(ssr)/./src/components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _admin_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./admin-layout */ \"(ssr)/./src/components/layout/admin-layout.tsx\");\n/* harmony import */ var _page_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page-header */ \"(ssr)/./src/components/layout/page-header.tsx\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBNkI7QUFDSztBQUNKO0FBQ0QiLCJzb3VyY2VzIjpbIkc6XFxwcm9qZWN0c1xcS29vbFNvZnRcXGtvb2xzb2Z0LXdlYlxcc3JjXFxjb21wb25lbnRzXFxsYXlvdXRcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vYmFzZS1sYXlvdXQnO1xuZXhwb3J0ICogZnJvbSAnLi9kYXNoYm9hcmQtbGF5b3V0JztcbmV4cG9ydCAqIGZyb20gJy4vYWRtaW4tbGF5b3V0JztcbmV4cG9ydCAqIGZyb20gJy4vcGFnZS1oZWFkZXInO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/page-header.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/page-header.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageHeader: () => (/* binding */ PageHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Home_LayoutDashboard_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,LayoutDashboard,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_LayoutDashboard_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Home,LayoutDashboard,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Home_LayoutDashboard_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Home,LayoutDashboard,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(ssr)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(ssr)/./src/lib/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ PageHeader auto */ \n\n\n\n\n\n\n\n/**\n * PageHeader Component\n *\n * A standardized header component for all pages in the application.\n * Includes breadcrumb navigation, page title, and action buttons.\n */ function PageHeader({ title, breadcrumbs, actions, showDashboardLink = true, showAdminLink = false }) {\n    const { user, isAdmin, logout } = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Default breadcrumbs if none provided\n    const defaultBreadcrumbs = breadcrumbs || [\n        {\n            label: 'Dashboard',\n            href: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_LayoutDashboard_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                lineNumber: 35,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            label: title,\n            current: true\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col space-y-3 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            breadcrumbs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.Breadcrumb, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbList, {\n                                    children: defaultBreadcrumbs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                            children: [\n                                                index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbSeparator, {}, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                                                    className: \"breadcrumb-item\",\n                                                    children: item.current ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-900 font-medium flex items-center\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                                lineNumber: 50,\n                                                                columnNumber: 43\n                                                            }, this),\n                                                            item.label\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 41\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbLink, {\n                                                        href: item.href || '#',\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: item.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                                lineNumber: 53,\n                                                                columnNumber: 43\n                                                            }, this),\n                                                            item.label\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 60\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 29\n                            }, this),\n                            (!breadcrumbs || defaultBreadcrumbs.length > 0 && defaultBreadcrumbs[defaultBreadcrumbs.length - 1].label !== title) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-gray-900 mt-1\",\n                                id: \"page-title\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 134\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: [\n                                    \"Welcome, \",\n                                    user.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 22\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    actions,\n                                    showDashboardLink && pathname !== '/dashboard' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        asChild: true,\n                                        variant: \"default\",\n                                        size: \"sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/dashboard\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_LayoutDashboard_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Dashboard\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 66\n                                    }, this),\n                                    showAdminLink && isAdmin() && pathname !== '/admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        asChild: true,\n                                        variant: \"default\",\n                                        size: \"sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/admin\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_LayoutDashboard_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Admin\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 71\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\page-header.tsx\",\n        lineNumber: 40,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/page-header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SessionProvider auto */ \n\n/**\n * Session Provider Component\n *\n * This component wraps the application with NextAuth.js session context.\n * It provides session state to all child components.\n *\n * @param children Child components\n * @returns Session provider component\n */ function SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        // Configure session refresh behavior\n        refetchInterval: 5 * 60,\n        refetchOnWindowFocus: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 16,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEU7QUFHNUU7Ozs7Ozs7O0NBUUEsR0FDTyxTQUFTQSxlQUFlQSxDQUFDLEVBQUVFLFFBQUFBLEVBQW1DLEVBQUU7SUFDckUscUJBQ0UsOERBQUMsNERBQXVCO1FBQ3RCO1FBQ0EsZUFBZSxDQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUN4QixvQkFBb0IsQ0FBQyxDQUFDLElBQUksQ0FBQztrQkFFMUJBLFFBQVE7Ozs7OztBQUdmIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxTZXNzaW9uUHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcblxuLyoqXG4gKiBTZXNzaW9uIFByb3ZpZGVyIENvbXBvbmVudFxuICpcbiAqIFRoaXMgY29tcG9uZW50IHdyYXBzIHRoZSBhcHBsaWNhdGlvbiB3aXRoIE5leHRBdXRoLmpzIHNlc3Npb24gY29udGV4dC5cbiAqIEl0IHByb3ZpZGVzIHNlc3Npb24gc3RhdGUgdG8gYWxsIGNoaWxkIGNvbXBvbmVudHMuXG4gKlxuICogQHBhcmFtIGNoaWxkcmVuIENoaWxkIGNvbXBvbmVudHNcbiAqIEByZXR1cm5zIFNlc3Npb24gcHJvdmlkZXIgY29tcG9uZW50XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlclxuICAgICAgLy8gQ29uZmlndXJlIHNlc3Npb24gcmVmcmVzaCBiZWhhdmlvclxuICAgICAgcmVmZXRjaEludGVydmFsPXs1ICogNjB9IC8vIFJlZnJlc2ggc2Vzc2lvbiBldmVyeSA1IG1pbnV0ZXNcbiAgICAgIHJlZmV0Y2hPbldpbmRvd0ZvY3VzPXt0cnVlfSAvLyBSZWZyZXNoIHdoZW4gd2luZG93IHJlZ2FpbnMgZm9jdXNcbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9OZXh0QXV0aFNlc3Npb25Qcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJOZXh0QXV0aFNlc3Npb25Qcm92aWRlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/breadcrumb.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/breadcrumb.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Breadcrumb: () => (/* binding */ Breadcrumb),\n/* harmony export */   BreadcrumbEllipsis: () => (/* binding */ BreadcrumbEllipsis),\n/* harmony export */   BreadcrumbItem: () => (/* binding */ BreadcrumbItem),\n/* harmony export */   BreadcrumbLink: () => (/* binding */ BreadcrumbLink),\n/* harmony export */   BreadcrumbList: () => (/* binding */ BreadcrumbList),\n/* harmony export */   BreadcrumbSeparator: () => (/* binding */ BreadcrumbSeparator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\n\nconst Breadcrumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        ref: ref,\n        \"aria-label\": \"breadcrumb\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-wrap items-center text-sm text-gray-500\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 9,\n        columnNumber: 12\n    }, undefined));\nBreadcrumb.displayName = \"Breadcrumb\";\nconst BreadcrumbList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-wrap items-center gap-1.5 break-words text-sm text-gray-500 sm:gap-2.5\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 14,\n        columnNumber: 12\n    }, undefined));\nBreadcrumbList.displayName = \"BreadcrumbList\";\nconst BreadcrumbItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center gap-1.5\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 19,\n        columnNumber: 12\n    }, undefined));\nBreadcrumbItem.displayName = \"BreadcrumbItem\";\nconst BreadcrumbLink = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ asChild, className, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : (next_link__WEBPACK_IMPORTED_MODULE_2___default());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"transition-colors hover:text-gray-900 hover:underline\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, undefined);\n});\nBreadcrumbLink.displayName = \"BreadcrumbLink\";\nconst BreadcrumbSeparator = ({ children, className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        role: \"presentation\",\n        \"aria-hidden\": \"true\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"[&>svg]:size-3.5 text-gray-400\", className),\n        ...props,\n        children: children || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n            lineNumber: 35,\n            columnNumber: 18\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 34,\n        columnNumber: 7\n    }, undefined);\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\";\nconst BreadcrumbEllipsis = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        role: \"presentation\",\n        \"aria-hidden\": \"true\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-9 w-9 items-center justify-center\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                lineNumber: 42,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"More\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                lineNumber: 43,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 41,\n        columnNumber: 7\n    }, undefined);\nBreadcrumbEllipsis.displayName = \"BreadcrumbElipssis\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/breadcrumb.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   ButtonLink: () => (/* binding */ ButtonLink),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-gray-300 bg-white text-black hover:bg-secondary hover:text-black\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-secondary hover:text-black\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_5__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 36,\n        columnNumber: 10\n    }, undefined);\n});\nButton.displayName = \"Button\";\nconst ButtonLink = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, href, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n        href: href,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 50,\n        columnNumber: 10\n    }, undefined);\n});\nButtonLink.displayName = \"ButtonLink\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border border-gray-200 bg-white text-black shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 6,\n        columnNumber: 12\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 11,\n        columnNumber: 12\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 16,\n        columnNumber: 12\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-text-secondary\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 21,\n        columnNumber: 12\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 26,\n        columnNumber: 12\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 31,\n        columnNumber: 12\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFFRTtBQUVoQyxNQUFNRSxJQUFJLGlCQUFHRiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxHQUFHLGlCQUM3Qiw4REFBQyxHQUFHO1FBQ0YsR0FBRyxDQUFDLENBQUNBLEdBQUcsQ0FBQztRQUNULFNBQVMsQ0FBQyxDQUFDTCw4Q0FBRSxDQUNYLGlFQUFpRSxFQUNqRUcsU0FDRixDQUFDLENBQUM7UUFDRixHQUFJQyxLQUFLOzs7Ozs7QUFHYkgsSUFBSSxDQUFDSyxXQUFXLEdBQUcsTUFBTTtBQUV6QixNQUFNQyxVQUFVLGlCQUFHUiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxHQUFHLGlCQUM3Qiw4REFBQyxHQUFHO1FBQ0YsR0FBRyxDQUFDLENBQUNBLEdBQUcsQ0FBQztRQUNULFNBQVMsQ0FBQyxDQUFDTCw4Q0FBRSxDQUFDLCtCQUErQixFQUFFRyxTQUFTLENBQUMsQ0FBQztRQUMxRCxHQUFJQyxLQUFLOzs7Ozs7QUFHYkcsVUFBVSxDQUFDRCxXQUFXLEdBQUcsWUFBWTtBQUVyQyxNQUFNRSxTQUFTLGlCQUFHVCw2Q0FBZ0IsQ0FHaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxHQUFHLGlCQUM3Qiw4REFBQyxFQUFFO1FBQ0QsR0FBRyxDQUFDLENBQUNBLEdBQUcsQ0FBQztRQUNULFNBQVMsQ0FBQyxDQUFDTCw4Q0FBRSxDQUNYLG9EQUFvRCxFQUNwREcsU0FDRixDQUFDLENBQUM7UUFDRixHQUFJQyxLQUFLOzs7Ozs7QUFHYkksU0FBUyxDQUFDRixXQUFXLEdBQUcsV0FBVztBQUVuQyxNQUFNRyxlQUFlLGlCQUFHViw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxHQUFHLGlCQUM3Qiw4REFBQyxDQUFDO1FBQ0EsR0FBRyxDQUFDLENBQUNBLEdBQUcsQ0FBQztRQUNULFNBQVMsQ0FBQyxDQUFDTCw4Q0FBRSxDQUFDLDZCQUE2QixFQUFFRyxTQUFTLENBQUMsQ0FBQztRQUN4RCxHQUFJQyxLQUFLOzs7Ozs7QUFHYkssZUFBZSxDQUFDSCxXQUFXLEdBQUcsaUJBQWlCO0FBRS9DLE1BQU1JLFdBQVcsaUJBQUdYLDZDQUFnQixDQUdsQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLEdBQUcsaUJBQzdCLDhEQUFDLEdBQUc7UUFBQyxHQUFHLENBQUMsQ0FBQ0EsR0FBRyxDQUFDO1FBQUMsU0FBUyxDQUFDLENBQUNMLDhDQUFFLENBQUMsVUFBVSxFQUFFRyxTQUFTLENBQUMsQ0FBQztRQUFDLEdBQUlDLEtBQUs7Ozs7OztBQUVoRU0sV0FBVyxDQUFDSixXQUFXLEdBQUcsYUFBYTtBQUV2QyxNQUFNSyxVQUFVLGlCQUFHWiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxHQUFHLGlCQUM3Qiw4REFBQyxHQUFHO1FBQ0YsR0FBRyxDQUFDLENBQUNBLEdBQUcsQ0FBQztRQUNULFNBQVMsQ0FBQyxDQUFDTCw4Q0FBRSxDQUFDLDRCQUE0QixFQUFFRyxTQUFTLENBQUMsQ0FBQztRQUN2RCxHQUFJQyxLQUFLOzs7Ozs7QUFHYk8sVUFBVSxDQUFDTCxXQUFXLEdBQUcsWUFBWTtBQUV5QyIsInNvdXJjZXMiOlsiRzpcXHByb2plY3RzXFxLb29sU29mdFxca29vbHNvZnQtd2ViXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxjYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IENhcmQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJyb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgYmctd2hpdGUgdGV4dC1ibGFjayBzaGFkb3ctc21cIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmQuZGlzcGxheU5hbWUgPSBcIkNhcmRcIlxuXG5jb25zdCBDYXJkSGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgcC02XCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRIZWFkZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRIZWFkZXJcIlxuXG5jb25zdCBDYXJkVGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8aDNcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZFRpdGxlLmRpc3BsYXlOYW1lID0gXCJDYXJkVGl0bGVcIlxuXG5jb25zdCBDYXJkRGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxwXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInRleHQtc20gdGV4dC10ZXh0LXNlY29uZGFyeVwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRGVzY3JpcHRpb24uZGlzcGxheU5hbWUgPSBcIkNhcmREZXNjcmlwdGlvblwiXG5cbmNvbnN0IENhcmRDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwicC02IHB0LTBcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkNhcmRDb250ZW50LmRpc3BsYXlOYW1lID0gXCJDYXJkQ29udGVudFwiXG5cbmNvbnN0IENhcmRGb290ZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBpdGVtcy1jZW50ZXIgcC02IHB0LTBcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZEZvb3Rlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEZvb3RlclwiXG5cbmV4cG9ydCB7IENhcmQsIENhcmRIZWFkZXIsIENhcmRGb290ZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkQ29udGVudCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkNhcmQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/custom-toaster.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/custom-toaster.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomToaster: () => (/* binding */ CustomToaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./src/components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ CustomToaster auto */ \n\n\nfunction CustomToaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    \"data-toast-id\": id,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 31\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 14\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\custom-toaster.tsx\",\n        lineNumber: 9,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/custom-toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton),\n/* harmony export */   SpanSkeleton: () => (/* binding */ SpanSkeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, as = \"div\", ...props }) {\n    const Component = as;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-gray-200\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n/**\n * A skeleton component that renders as a span, safe to use inside paragraph elements.\n */ function SpanSkeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-gray-200 inline-block\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 12,\n        columnNumber: 12\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border border-gray-200 bg-white text-black\",\n            success: \"border-primary bg-primary text-white\",\n            destructive: \"border-destructive bg-destructive text-white\",\n            warning: \"border-warning bg-warning text-black\",\n            info: \"border-info bg-info text-white\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, 'data-toast-id': dataToastId, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        \"data-toast-id\": dataToastId,\n        \"data-variant\": variant || 'default',\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 34,\n        columnNumber: 10\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.success]:border-primary/40 group-[.success]:hover:border-primary/30 group-[.success]:hover:bg-primary group-[.success]:hover:text-primary-foreground group-[.success]:focus:ring-primary group-[.destructive]:border-destructive/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive group-[.warning]:border-warning/40 group-[.warning]:hover:border-warning/30 group-[.warning]:hover:bg-warning group-[.warning]:hover:text-warning-foreground group-[.warning]:focus:ring-warning group-[.info]:border-info/40 group-[.info]:hover:border-info/30 group-[.info]:hover:bg-info group-[.info]:hover:text-info-foreground group-[.info]:focus:ring-info\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 42,\n        columnNumber: 12\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 opacity-70 transition-opacity hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100\", \"data-[variant=default]:text-gray-500 data-[variant=default]:hover:text-gray-900 data-[variant=default]:focus:ring-gray-400\", \"data-[variant=success]:text-white data-[variant=success]:focus:ring-primary-light\", \"data-[variant=destructive]:text-white data-[variant=destructive]:focus:ring-destructive-light\", \"data-[variant=warning]:text-black data-[variant=warning]:focus:ring-warning-light\", \"data-[variant=info]:text-white data-[variant=info]:focus:ring-info-light\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 48,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 47,\n        columnNumber: 12\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 54,\n        columnNumber: 12\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 59,\n        columnNumber: 12\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/use-toast.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/use-toast.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 5;\nconst TOAST_REMOVE_DELAY = 5000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_VALUE;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/use-toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/hooks/useAuth.ts":
/*!**********************************!*\
  !*** ./src/lib/hooks/useAuth.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\n\n/**\n * Custom hook for authentication\n *\n * This hook provides:\n * - Authentication state (loading, authenticated, user data)\n * - Login and logout functions\n * - Role-based access control helpers\n */ function useAuth() {\n    const { data: session, status, update } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Log session data for debugging\n    console.log('useAuth Hook:', {\n        status,\n        sessionExists: !!session,\n        userExists: !!session?.user,\n        userRole: session?.user?.role,\n        isRefreshing\n    });\n    // Force refresh the session if it's taking too long to load\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            let timeoutId;\n            if (status === 'loading' && !isRefreshing) {\n                // If still loading after 2 seconds, try to refresh the session\n                timeoutId = setTimeout({\n                    \"useAuth.useEffect\": ()=>{\n                        console.log('useAuth: Session loading timeout, forcing refresh');\n                        setIsRefreshing(true);\n                        update() // Force refresh the session\n                        .then({\n                            \"useAuth.useEffect\": ()=>{\n                                console.log('useAuth: Session refreshed successfully');\n                            }\n                        }[\"useAuth.useEffect\"]).catch({\n                            \"useAuth.useEffect\": (err)=>{\n                                console.error('useAuth: Error refreshing session', err);\n                            }\n                        }[\"useAuth.useEffect\"]).finally({\n                            \"useAuth.useEffect\": ()=>{\n                                setIsRefreshing(false);\n                            }\n                        }[\"useAuth.useEffect\"]);\n                    }\n                }[\"useAuth.useEffect\"], 2000);\n            }\n            return ({\n                \"useAuth.useEffect\": ()=>{\n                    if (timeoutId) clearTimeout(timeoutId);\n                }\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], [\n        status,\n        isRefreshing,\n        update\n    ]);\n    // Check if the user is authenticated\n    const isAuthenticated = status === 'authenticated' && !!session?.user;\n    // Check if the authentication state is loading\n    const isLoading = status === 'loading' || isRefreshing;\n    // Get the user's role (with fallback to empty string to avoid null errors)\n    const userRole = session?.user?.role || '';\n    // Log authentication state\n    console.log('useAuth State:', {\n        isAuthenticated,\n        isLoading,\n        userRole,\n        status\n    });\n    /**\n   * Login function\n   * @param email User email\n   * @param password User password\n   * @param callbackUrl URL to redirect to after successful login\n   * @returns Promise resolving to login success status\n   */ const login = async (email, password, callbackUrl)=>{\n        try {\n            setError(null);\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signIn)('credentials', {\n                redirect: false,\n                email,\n                password\n            });\n            if (result?.error) {\n                setError('Invalid email or password');\n                return false;\n            }\n            if (result?.ok) {\n                router.push(callbackUrl || '/dashboard');\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error('Login error:', error);\n            setError('An error occurred during login');\n            return false;\n        }\n    };\n    /**\n   * Logout function\n   * @param callbackUrl URL to redirect to after logout\n   */ const logout = async (callbackUrl)=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.signOut)({\n            redirect: false\n        });\n        router.push(callbackUrl || '/auth/login');\n    };\n    /**\n   * Check if the user has a specific role\n   * @param role Role to check\n   * @returns Boolean indicating if the user has the role\n   */ const hasRole = (role)=>{\n        if (!userRole) return false;\n        if (Array.isArray(role)) {\n            return role.includes(userRole);\n        }\n        return userRole === role;\n    };\n    /**\n   * Check if the user is an admin\n   * @returns Boolean indicating if the user is an admin\n   */ const isAdmin = ()=>userRole === 'ADMIN';\n    /**\n   * Check if the user is a manager\n   * @returns Boolean indicating if the user is a manager\n   */ const isManager = ()=>userRole === 'MANAGER';\n    /**\n   * Check if the user is an executive\n   * @returns Boolean indicating if the user is an executive\n   */ const isExecutive = ()=>userRole === 'EXECUTIVE';\n    return {\n        user: session?.user || null,\n        isAuthenticated,\n        isLoading,\n        userRole,\n        login,\n        logout,\n        error,\n        hasRole,\n        isAdmin,\n        isManager,\n        isExecutive\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeWords: () => (/* binding */ capitalizeWords),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),\n/* harmony export */   truncateString: () => (/* binding */ truncateString)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Combines class names with Tailwind CSS\n * @param inputs - Class names to combine\n * @returns Combined class names\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Formats a date string or Date object into a human-readable format\n * @param date - Date to format\n * @param format - Optional format (default: 'MMM dd, yyyy')\n * @returns Formatted date string\n */ function formatDate(date, format = 'MMM dd, yyyy') {\n    if (!date) return '';\n    try {\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) {\n            return '';\n        }\n        // Basic formatting options\n        const options = {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric'\n        };\n        // Add time if format includes time\n        if (format.includes('HH') || format.includes('hh') || format.includes('mm') || format.includes('ss')) {\n            options.hour = '2-digit';\n            options.minute = '2-digit';\n        }\n        return dateObj.toLocaleDateString('en-US', options);\n    } catch (error) {\n        console.error('Error formatting date:', error);\n        return '';\n    }\n}\n/**\n * Truncates a string to a specified length and adds ellipsis\n * @param str - String to truncate\n * @param length - Maximum length\n * @returns Truncated string\n */ function truncateString(str, length = 50) {\n    if (!str) return '';\n    if (str.length <= length) return str;\n    return str.substring(0, length) + '...';\n}\n/**\n * Formats a number as currency\n * @param value - Number to format\n * @param currency - Currency code (default: 'INR')\n * @returns Formatted currency string\n */ function formatCurrency(value, currency = 'INR') {\n    if (value === null || value === undefined) return '';\n    const numValue = typeof value === 'string' ? parseFloat(value) : value;\n    if (isNaN(numValue)) return '';\n    return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(numValue);\n}\n/**\n * Capitalizes the first letter of each word in a string\n * @param str - String to capitalize\n * @returns Capitalized string\n */ function capitalizeWords(str) {\n    if (!str) return '';\n    return str.split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ');\n}\n/**\n * Generates a random string of specified length\n * @param length - Length of the random string\n * @returns Random string\n */ function generateRandomString(length = 8) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Debounces a function\n * @param func - Function to debounce\n * @param wait - Wait time in milliseconds\n * @returns Debounced function\n */ function debounce(func, wait) {\n    let timeout = null;\n    return function(...args) {\n        const later = ()=>{\n            timeout = null;\n            func(...args);\n        };\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n}\n/**\n * Validates an email address\n * @param email - Email to validate\n * @returns Whether the email is valid\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Formats a phone number\n * @param phone - Phone number to format\n * @returns Formatted phone number\n */ function formatPhoneNumber(phone) {\n    if (!phone) return '';\n    // Remove all non-numeric characters\n    const cleaned = phone.replace(/\\D/g, '');\n    // Format based on length\n    if (cleaned.length === 10) {\n        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    return phone;\n}\n/**\n * Converts a string to kebab-case\n * @param str - String to convert\n * @returns Kebab-case string\n */ function toKebabCase(str) {\n    if (!str) return '';\n    return str.replace(/([a-z])([A-Z])/g, '$1-$2').replace(/\\s+/g, '-').toLowerCase();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE0QztBQUNKO0FBRXhDOzs7O0NBSUEsR0FDTyxTQUFTRSxFQUFFQSxDQUFDLEdBQUdDLE1BQW9CLEVBQUU7SUFDMUMsT0FBT0YsdURBQU8sQ0FBQ0QsMENBQUksQ0FBQ0csTUFBTSxDQUFDLENBQUM7QUFDOUI7QUFFQTs7Ozs7Q0FLQSxHQUNPLFNBQVNDLFVBQVVBLENBQUNDLElBQXNDLEVBQUVDLE1BQWMsR0FBRyxjQUFjLEVBQVU7SUFDMUcsSUFBSSxDQUFDRCxJQUFJLEVBQUUsT0FBTyxFQUFFO0lBRXBCLElBQUk7UUFDRixNQUFNRSxPQUFPLEdBQUcsT0FBT0YsSUFBSSxLQUFLLFFBQVEsR0FBRyxJQUFJRyxJQUFJLENBQUNILElBQUksQ0FBQyxHQUFHQSxJQUFJO1FBRWhFLElBQUlJLEtBQUssQ0FBQ0YsT0FBTyxDQUFDRyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUU7WUFDNUIsT0FBTyxFQUFFO1FBQ1g7UUFFQTtRQUNBLE1BQU1DLE9BQW1DLEdBQUc7WUFDMUNDLElBQUksRUFBRSxTQUFTO1lBQ2ZDLEtBQUssRUFBRSxPQUFPO1lBQ2RDLEdBQUcsRUFBRTtRQUNQLENBQUM7UUFFRDtRQUNBLElBQUlSLE1BQU0sQ0FBQ1MsUUFBUSxDQUFDLElBQUksQ0FBQyxJQUFJVCxNQUFNLENBQUNTLFFBQVEsQ0FBQyxJQUFJLENBQUMsSUFBSVQsTUFBTSxDQUFDUyxRQUFRLENBQUMsSUFBSSxDQUFDLElBQUlULE1BQU0sQ0FBQ1MsUUFBUSxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ3BHSixPQUFPLENBQUNLLElBQUksR0FBRyxTQUFTO1lBQ3hCTCxPQUFPLENBQUNNLE1BQU0sR0FBRyxTQUFTO1FBQzVCO1FBRUEsT0FBT1YsT0FBTyxDQUFDVyxrQkFBa0IsQ0FBQyxPQUFPLEVBQUVQLE9BQU8sQ0FBQztJQUNyRCxDQUFDLENBQUMsT0FBT1EsS0FBSyxFQUFFO1FBQ2RDLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLHdCQUF3QixFQUFFQSxLQUFLLENBQUM7UUFDOUMsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBOzs7OztDQUtBLEdBQ08sU0FBU0UsY0FBY0EsQ0FBQ0MsR0FBVyxFQUFFQyxNQUFjLEdBQUcsRUFBRSxFQUFVO0lBQ3ZFLElBQUksQ0FBQ0QsR0FBRyxFQUFFLE9BQU8sRUFBRTtJQUNuQixJQUFJQSxHQUFHLENBQUNDLE1BQU0sSUFBSUEsTUFBTSxFQUFFLE9BQU9ELEdBQUc7SUFDcEMsT0FBT0EsR0FBRyxDQUFDRSxTQUFTLENBQUMsQ0FBQyxFQUFFRCxNQUFNLENBQUMsR0FBRyxLQUFLO0FBQ3pDO0FBRUE7Ozs7O0NBS0EsR0FDTyxTQUFTRSxjQUFjQSxDQUFDQyxLQUFzQixFQUFFQyxRQUFnQixHQUFHLEtBQUssRUFBVTtJQUN2RixJQUFJRCxLQUFLLEtBQUssSUFBSSxJQUFJQSxLQUFLLEtBQUtFLFNBQVMsRUFBRSxPQUFPLEVBQUU7SUFFcEQsTUFBTUMsUUFBUSxHQUFHLE9BQU9ILEtBQUssS0FBSyxRQUFRLEdBQUdJLFVBQVUsQ0FBQ0osS0FBSyxDQUFDLEdBQUdBLEtBQUs7SUFFdEUsSUFBSWpCLEtBQUssQ0FBQ29CLFFBQVEsQ0FBQyxFQUFFLE9BQU8sRUFBRTtJQUU5QixPQUFPLElBQUlFLElBQUksQ0FBQ0MsWUFBWSxDQUFDLE9BQU8sRUFBRTtRQUNwQ0MsS0FBSyxFQUFFLFVBQVU7UUFDakJOLFFBQVE7UUFDUk8scUJBQXFCLEVBQUUsQ0FBQztRQUN4QkMscUJBQXFCLEVBQUU7SUFDekIsQ0FBQyxDQUFDLENBQUM3QixNQUFNLENBQUN1QixRQUFRLENBQUM7QUFDckI7QUFFQTs7OztDQUlBLEdBQ08sU0FBU08sZUFBZUEsQ0FBQ2QsR0FBVyxFQUFVO0lBQ25ELElBQUksQ0FBQ0EsR0FBRyxFQUFFLE9BQU8sRUFBRTtJQUNuQixPQUFPQSxHQUFHLENBQ1BlLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FDVkMsR0FBRyxFQUFDQyxJQUFJLEdBQUlBLElBQUksQ0FBQ0MsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDQyxXQUFXLENBQUMsQ0FBQyxHQUFHRixJQUFJLENBQUNHLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUN2RUMsSUFBSSxDQUFDLEdBQUcsQ0FBQztBQUNkO0FBRUE7Ozs7Q0FJQSxHQUNPLFNBQVNDLG9CQUFvQkEsQ0FBQ3RCLE1BQWMsR0FBRyxDQUFDLEVBQVU7SUFDL0QsTUFBTXVCLEtBQUssR0FBRyxnRUFBZ0U7SUFDOUUsSUFBSUMsTUFBTSxHQUFHLEVBQUU7SUFDZixJQUFLLElBQUlDLENBQUMsR0FBRyxDQUFDLEVBQUVBLENBQUMsR0FBR3pCLE1BQU0sRUFBRXlCLENBQUMsRUFBRSxDQUFFO1FBQy9CRCxNQUFNLElBQUlELEtBQUssQ0FBQ04sTUFBTSxDQUFDUyxJQUFJLENBQUNDLEtBQUssQ0FBQ0QsSUFBSSxDQUFDRSxNQUFNLENBQUMsQ0FBQyxHQUFHTCxLQUFLLENBQUN2QixNQUFNLENBQUMsQ0FBQztJQUNsRTtJQUNBLE9BQU93QixNQUFNO0FBQ2Y7QUFFQTs7Ozs7Q0FLQSxHQUNPLFNBQVNLLFFBQVFBLENBQ3RCQyxJQUFPLEVBQ1BDLElBQVksRUFDc0I7SUFDbEMsSUFBSUMsT0FBOEIsR0FBRyxJQUFJO0lBRXpDLE9BQU8sU0FBUyxHQUFHQyxJQUFtQixFQUFRO1FBQzVDLE1BQU1DLEtBQUssR0FBR0EsQ0FBQTtZQUNaRixPQUFPLEdBQUcsSUFBSTtZQUNkRixJQUFJLENBQUMsR0FBR0csSUFBSSxDQUFDO1FBQ2YsQ0FBQztRQUVELElBQUlELE9BQU8sRUFBRUcsWUFBWSxDQUFDSCxPQUFPLENBQUM7UUFDbENBLE9BQU8sR0FBR0ksVUFBVSxDQUFDRixLQUFLLEVBQUVILElBQUksQ0FBQztJQUNuQyxDQUFDO0FBQ0g7QUFFQTs7OztDQUlBLEdBQ08sU0FBU00sWUFBWUEsQ0FBQ0MsS0FBYSxFQUFXO0lBQ25ELE1BQU1DLFVBQVUsR0FBRyw0QkFBNEI7SUFDL0MsT0FBT0EsVUFBVSxDQUFDQyxJQUFJLENBQUNGLEtBQUssQ0FBQztBQUMvQjtBQUVBOzs7O0NBSUEsR0FDTyxTQUFTRyxpQkFBaUJBLENBQUNDLEtBQWEsRUFBVTtJQUN2RCxJQUFJLENBQUNBLEtBQUssRUFBRSxPQUFPLEVBQUU7SUFFckI7SUFDQSxNQUFNQyxPQUFPLEdBQUdELEtBQUssQ0FBQ0UsT0FBTyxDQUFDLEtBQUssRUFBRSxFQUFFLENBQUM7SUFFeEM7SUFDQSxJQUFJRCxPQUFPLENBQUMzQyxNQUFNLEtBQUssRUFBRSxFQUFFO1FBQ3pCLE9BQVEsSUFBRzJDLE9BQU8sQ0FBQ3hCLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFFLEtBQUl3QixPQUFPLENBQUN4QixLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBRSxJQUFHd0IsT0FBTyxDQUFDeEIsS0FBSyxDQUFDLENBQUMsQ0FBRSxFQUFDO0lBQzlFO0lBRUEsT0FBT3VCLEtBQUs7QUFDZDtBQUVBOzs7O0NBSUEsR0FDTyxTQUFTRyxXQUFXQSxDQUFDOUMsR0FBVyxFQUFVO0lBQy9DLElBQUksQ0FBQ0EsR0FBRyxFQUFFLE9BQU8sRUFBRTtJQUNuQixPQUFPQSxHQUFHLENBQ1A2QyxPQUFPLENBQUMsaUJBQWlCLEVBQUUsT0FBTyxDQUFDLENBQ25DQSxPQUFPLENBQUMsTUFBTSxFQUFFLEdBQUcsQ0FBQyxDQUNwQnhCLFdBQVcsQ0FBQyxDQUFDO0FBQ2xCIiwic291cmNlcyI6WyJHOlxccHJvamVjdHNcXEtvb2xTb2Z0XFxrb29sc29mdC13ZWJcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tICdjbHN4JztcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tICd0YWlsd2luZC1tZXJnZSc7XG5cbi8qKlxuICogQ29tYmluZXMgY2xhc3MgbmFtZXMgd2l0aCBUYWlsd2luZCBDU1NcbiAqIEBwYXJhbSBpbnB1dHMgLSBDbGFzcyBuYW1lcyB0byBjb21iaW5lXG4gKiBAcmV0dXJucyBDb21iaW5lZCBjbGFzcyBuYW1lc1xuICovXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cblxuLyoqXG4gKiBGb3JtYXRzIGEgZGF0ZSBzdHJpbmcgb3IgRGF0ZSBvYmplY3QgaW50byBhIGh1bWFuLXJlYWRhYmxlIGZvcm1hdFxuICogQHBhcmFtIGRhdGUgLSBEYXRlIHRvIGZvcm1hdFxuICogQHBhcmFtIGZvcm1hdCAtIE9wdGlvbmFsIGZvcm1hdCAoZGVmYXVsdDogJ01NTSBkZCwgeXl5eScpXG4gKiBAcmV0dXJucyBGb3JtYXR0ZWQgZGF0ZSBzdHJpbmdcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZTogRGF0ZSB8IHN0cmluZyB8IG51bGwgfCB1bmRlZmluZWQsIGZvcm1hdDogc3RyaW5nID0gJ01NTSBkZCwgeXl5eScpOiBzdHJpbmcge1xuICBpZiAoIWRhdGUpIHJldHVybiAnJztcbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgZGF0ZU9iaiA9IHR5cGVvZiBkYXRlID09PSAnc3RyaW5nJyA/IG5ldyBEYXRlKGRhdGUpIDogZGF0ZTtcbiAgICBcbiAgICBpZiAoaXNOYU4oZGF0ZU9iai5nZXRUaW1lKCkpKSB7XG4gICAgICByZXR1cm4gJyc7XG4gICAgfVxuICAgIFxuICAgIC8vIEJhc2ljIGZvcm1hdHRpbmcgb3B0aW9uc1xuICAgIGNvbnN0IG9wdGlvbnM6IEludGwuRGF0ZVRpbWVGb3JtYXRPcHRpb25zID0ge1xuICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgbW9udGg6ICdzaG9ydCcsXG4gICAgICBkYXk6ICdudW1lcmljJyxcbiAgICB9O1xuICAgIFxuICAgIC8vIEFkZCB0aW1lIGlmIGZvcm1hdCBpbmNsdWRlcyB0aW1lXG4gICAgaWYgKGZvcm1hdC5pbmNsdWRlcygnSEgnKSB8fCBmb3JtYXQuaW5jbHVkZXMoJ2hoJykgfHwgZm9ybWF0LmluY2x1ZGVzKCdtbScpIHx8IGZvcm1hdC5pbmNsdWRlcygnc3MnKSkge1xuICAgICAgb3B0aW9ucy5ob3VyID0gJzItZGlnaXQnO1xuICAgICAgb3B0aW9ucy5taW51dGUgPSAnMi1kaWdpdCc7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBkYXRlT2JqLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCBvcHRpb25zKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmb3JtYXR0aW5nIGRhdGU6JywgZXJyb3IpO1xuICAgIHJldHVybiAnJztcbiAgfVxufVxuXG4vKipcbiAqIFRydW5jYXRlcyBhIHN0cmluZyB0byBhIHNwZWNpZmllZCBsZW5ndGggYW5kIGFkZHMgZWxsaXBzaXNcbiAqIEBwYXJhbSBzdHIgLSBTdHJpbmcgdG8gdHJ1bmNhdGVcbiAqIEBwYXJhbSBsZW5ndGggLSBNYXhpbXVtIGxlbmd0aFxuICogQHJldHVybnMgVHJ1bmNhdGVkIHN0cmluZ1xuICovXG5leHBvcnQgZnVuY3Rpb24gdHJ1bmNhdGVTdHJpbmcoc3RyOiBzdHJpbmcsIGxlbmd0aDogbnVtYmVyID0gNTApOiBzdHJpbmcge1xuICBpZiAoIXN0cikgcmV0dXJuICcnO1xuICBpZiAoc3RyLmxlbmd0aCA8PSBsZW5ndGgpIHJldHVybiBzdHI7XG4gIHJldHVybiBzdHIuc3Vic3RyaW5nKDAsIGxlbmd0aCkgKyAnLi4uJztcbn1cblxuLyoqXG4gKiBGb3JtYXRzIGEgbnVtYmVyIGFzIGN1cnJlbmN5XG4gKiBAcGFyYW0gdmFsdWUgLSBOdW1iZXIgdG8gZm9ybWF0XG4gKiBAcGFyYW0gY3VycmVuY3kgLSBDdXJyZW5jeSBjb2RlIChkZWZhdWx0OiAnSU5SJylcbiAqIEByZXR1cm5zIEZvcm1hdHRlZCBjdXJyZW5jeSBzdHJpbmdcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEN1cnJlbmN5KHZhbHVlOiBudW1iZXIgfCBzdHJpbmcsIGN1cnJlbmN5OiBzdHJpbmcgPSAnSU5SJyk6IHN0cmluZyB7XG4gIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSByZXR1cm4gJyc7XG4gIFxuICBjb25zdCBudW1WYWx1ZSA9IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgPyBwYXJzZUZsb2F0KHZhbHVlKSA6IHZhbHVlO1xuICBcbiAgaWYgKGlzTmFOKG51bVZhbHVlKSkgcmV0dXJuICcnO1xuICBcbiAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgnZW4tSU4nLCB7XG4gICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgY3VycmVuY3ksXG4gICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLFxuICAgIG1heGltdW1GcmFjdGlvbkRpZ2l0czogMixcbiAgfSkuZm9ybWF0KG51bVZhbHVlKTtcbn1cblxuLyoqXG4gKiBDYXBpdGFsaXplcyB0aGUgZmlyc3QgbGV0dGVyIG9mIGVhY2ggd29yZCBpbiBhIHN0cmluZ1xuICogQHBhcmFtIHN0ciAtIFN0cmluZyB0byBjYXBpdGFsaXplXG4gKiBAcmV0dXJucyBDYXBpdGFsaXplZCBzdHJpbmdcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhcGl0YWxpemVXb3JkcyhzdHI6IHN0cmluZyk6IHN0cmluZyB7XG4gIGlmICghc3RyKSByZXR1cm4gJyc7XG4gIHJldHVybiBzdHJcbiAgICAuc3BsaXQoJyAnKVxuICAgIC5tYXAod29yZCA9PiB3b3JkLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgd29yZC5zbGljZSgxKS50b0xvd2VyQ2FzZSgpKVxuICAgIC5qb2luKCcgJyk7XG59XG5cbi8qKlxuICogR2VuZXJhdGVzIGEgcmFuZG9tIHN0cmluZyBvZiBzcGVjaWZpZWQgbGVuZ3RoXG4gKiBAcGFyYW0gbGVuZ3RoIC0gTGVuZ3RoIG9mIHRoZSByYW5kb20gc3RyaW5nXG4gKiBAcmV0dXJucyBSYW5kb20gc3RyaW5nXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZVJhbmRvbVN0cmluZyhsZW5ndGg6IG51bWJlciA9IDgpOiBzdHJpbmcge1xuICBjb25zdCBjaGFycyA9ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSc7XG4gIGxldCByZXN1bHQgPSAnJztcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW5ndGg7IGkrKykge1xuICAgIHJlc3VsdCArPSBjaGFycy5jaGFyQXQoTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogY2hhcnMubGVuZ3RoKSk7XG4gIH1cbiAgcmV0dXJuIHJlc3VsdDtcbn1cblxuLyoqXG4gKiBEZWJvdW5jZXMgYSBmdW5jdGlvblxuICogQHBhcmFtIGZ1bmMgLSBGdW5jdGlvbiB0byBkZWJvdW5jZVxuICogQHBhcmFtIHdhaXQgLSBXYWl0IHRpbWUgaW4gbWlsbGlzZWNvbmRzXG4gKiBAcmV0dXJucyBEZWJvdW5jZWQgZnVuY3Rpb25cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlYm91bmNlPFQgZXh0ZW5kcyAoLi4uYXJnczogYW55W10pID0+IGFueT4oXG4gIGZ1bmM6IFQsXG4gIHdhaXQ6IG51bWJlclxuKTogKC4uLmFyZ3M6IFBhcmFtZXRlcnM8VD4pID0+IHZvaWQge1xuICBsZXQgdGltZW91dDogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbDtcbiAgXG4gIHJldHVybiBmdW5jdGlvbiguLi5hcmdzOiBQYXJhbWV0ZXJzPFQ+KTogdm9pZCB7XG4gICAgY29uc3QgbGF0ZXIgPSAoKSA9PiB7XG4gICAgICB0aW1lb3V0ID0gbnVsbDtcbiAgICAgIGZ1bmMoLi4uYXJncyk7XG4gICAgfTtcbiAgICBcbiAgICBpZiAodGltZW91dCkgY2xlYXJUaW1lb3V0KHRpbWVvdXQpO1xuICAgIHRpbWVvdXQgPSBzZXRUaW1lb3V0KGxhdGVyLCB3YWl0KTtcbiAgfTtcbn1cblxuLyoqXG4gKiBWYWxpZGF0ZXMgYW4gZW1haWwgYWRkcmVzc1xuICogQHBhcmFtIGVtYWlsIC0gRW1haWwgdG8gdmFsaWRhdGVcbiAqIEByZXR1cm5zIFdoZXRoZXIgdGhlIGVtYWlsIGlzIHZhbGlkXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc1ZhbGlkRW1haWwoZW1haWw6IHN0cmluZyk6IGJvb2xlYW4ge1xuICBjb25zdCBlbWFpbFJlZ2V4ID0gL15bXlxcc0BdK0BbXlxcc0BdK1xcLlteXFxzQF0rJC87XG4gIHJldHVybiBlbWFpbFJlZ2V4LnRlc3QoZW1haWwpO1xufVxuXG4vKipcbiAqIEZvcm1hdHMgYSBwaG9uZSBudW1iZXJcbiAqIEBwYXJhbSBwaG9uZSAtIFBob25lIG51bWJlciB0byBmb3JtYXRcbiAqIEByZXR1cm5zIEZvcm1hdHRlZCBwaG9uZSBudW1iZXJcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFBob25lTnVtYmVyKHBob25lOiBzdHJpbmcpOiBzdHJpbmcge1xuICBpZiAoIXBob25lKSByZXR1cm4gJyc7XG4gIFxuICAvLyBSZW1vdmUgYWxsIG5vbi1udW1lcmljIGNoYXJhY3RlcnNcbiAgY29uc3QgY2xlYW5lZCA9IHBob25lLnJlcGxhY2UoL1xcRC9nLCAnJyk7XG4gIFxuICAvLyBGb3JtYXQgYmFzZWQgb24gbGVuZ3RoXG4gIGlmIChjbGVhbmVkLmxlbmd0aCA9PT0gMTApIHtcbiAgICByZXR1cm4gYCgke2NsZWFuZWQuc2xpY2UoMCwgMyl9KSAke2NsZWFuZWQuc2xpY2UoMywgNil9LSR7Y2xlYW5lZC5zbGljZSg2KX1gO1xuICB9XG4gIFxuICByZXR1cm4gcGhvbmU7XG59XG5cbi8qKlxuICogQ29udmVydHMgYSBzdHJpbmcgdG8ga2ViYWItY2FzZVxuICogQHBhcmFtIHN0ciAtIFN0cmluZyB0byBjb252ZXJ0XG4gKiBAcmV0dXJucyBLZWJhYi1jYXNlIHN0cmluZ1xuICovXG5leHBvcnQgZnVuY3Rpb24gdG9LZWJhYkNhc2Uoc3RyOiBzdHJpbmcpOiBzdHJpbmcge1xuICBpZiAoIXN0cikgcmV0dXJuICcnO1xuICByZXR1cm4gc3RyXG4gICAgLnJlcGxhY2UoLyhbYS16XSkoW0EtWl0pL2csICckMS0kMicpXG4gICAgLnJlcGxhY2UoL1xccysvZywgJy0nKVxuICAgIC50b0xvd2VyQ2FzZSgpO1xufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJmb3JtYXREYXRlIiwiZGF0ZSIsImZvcm1hdCIsImRhdGVPYmoiLCJEYXRlIiwiaXNOYU4iLCJnZXRUaW1lIiwib3B0aW9ucyIsInllYXIiLCJtb250aCIsImRheSIsImluY2x1ZGVzIiwiaG91ciIsIm1pbnV0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImVycm9yIiwiY29uc29sZSIsInRydW5jYXRlU3RyaW5nIiwic3RyIiwibGVuZ3RoIiwic3Vic3RyaW5nIiwiZm9ybWF0Q3VycmVuY3kiLCJ2YWx1ZSIsImN1cnJlbmN5IiwidW5kZWZpbmVkIiwibnVtVmFsdWUiLCJwYXJzZUZsb2F0IiwiSW50bCIsIk51bWJlckZvcm1hdCIsInN0eWxlIiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwiY2FwaXRhbGl6ZVdvcmRzIiwic3BsaXQiLCJtYXAiLCJ3b3JkIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsInRvTG93ZXJDYXNlIiwiam9pbiIsImdlbmVyYXRlUmFuZG9tU3RyaW5nIiwiY2hhcnMiLCJyZXN1bHQiLCJpIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwiZGVib3VuY2UiLCJmdW5jIiwid2FpdCIsInRpbWVvdXQiLCJhcmdzIiwibGF0ZXIiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0IiwiaXNWYWxpZEVtYWlsIiwiZW1haWwiLCJlbWFpbFJlZ2V4IiwidGVzdCIsImZvcm1hdFBob25lTnVtYmVyIiwicGhvbmUiLCJjbGVhbmVkIiwicmVwbGFjZSIsInRvS2ViYWJDYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Freference-data%2Fpage&page=%2Freference-data%2Fpage&appPaths=%2Freference-data%2Fpage&pagePath=private-next-app-dir%2Freference-data%2Fpage.tsx&appDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5Cprojects%5CKoolSoft%5Ckoolsoft-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();