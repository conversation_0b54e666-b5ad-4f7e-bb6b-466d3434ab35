"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/new/page",{

/***/ "(app-pages-browser)/./src/app/warranties/new/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/warranties/new/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewWarrantyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,DollarSign,Plus,Save,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * New Warranty Page\n *\n * This page provides a form for creating new warranties with support for\n * different warranty types (in-warranty, out-warranty, BLUESTAR-specific).\n */ function NewWarrantyPage() {\n    _s();\n    _s1();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const warrantyType = searchParams.get('type') || 'in-warranty';\n    const vendor = searchParams.get('vendor');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: '',\n        executiveId: '',\n        contactPersonId: '',\n        bslNo: '',\n        bslDate: '',\n        bslAmount: '',\n        frequency: '',\n        numberOfMachines: '1',\n        installDate: '',\n        warrantyDate: '',\n        warningDate: '',\n        technicianId: '',\n        amcId: '',\n        status: 'ACTIVE',\n        // BLUESTAR specific fields\n        bluestarWarrantyCode: '',\n        bluestarServiceCenter: '',\n        bluestarContactPerson: '',\n        bluestarContactPhone: '',\n        specialTerms: ''\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('basic');\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [executives, setExecutives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingData, setIsLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Load customers and executives\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewWarrantyPage.useEffect\": ()=>{\n            const loadFormData = {\n                \"NewWarrantyPage.useEffect.loadFormData\": async ()=>{\n                    try {\n                        setIsLoadingData(true);\n                        const [customersRes, executivesRes] = await Promise.all([\n                            fetch('/api/customers?take=1000', {\n                                credentials: 'include'\n                            }),\n                            fetch('/api/users?role=EXECUTIVE&take=1000', {\n                                credentials: 'include'\n                            })\n                        ]);\n                        if (customersRes.ok) {\n                            const customersData = await customersRes.json();\n                            setCustomers(customersData.customers || []);\n                        }\n                        if (executivesRes.ok) {\n                            const executivesData = await executivesRes.json();\n                            setExecutives(executivesData.users || []);\n                        }\n                    } catch (error) {\n                        console.error('Error loading form data:', error);\n                    } finally{\n                        setIsLoadingData(false);\n                    }\n                }\n            }[\"NewWarrantyPage.useEffect.loadFormData\"];\n            loadFormData();\n        }\n    }[\"NewWarrantyPage.useEffect\"], []);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.customerId) {\n            newErrors.customerId = 'Customer is required';\n        }\n        if (!formData.bslNo) {\n            newErrors.bslNo = 'BSL Number is required';\n        }\n        if (!formData.numberOfMachines || parseInt(formData.numberOfMachines) < 1) {\n            newErrors.numberOfMachines = 'Number of machines must be at least 1';\n        }\n        if (formData.installDate && formData.warrantyDate) {\n            const installDate = new Date(formData.installDate);\n            const warrantyDate = new Date(formData.warrantyDate);\n            if (warrantyDate <= installDate) {\n                newErrors.warrantyDate = 'Warranty date must be after install date';\n            }\n        }\n        if (vendor === 'bluestar' && !formData.bluestarWarrantyCode) {\n            newErrors.bluestarWarrantyCode = 'BLUESTAR warranty code is required';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // Prepare warranty data for API\n            const warrantyData = {\n                customerId: formData.customerId,\n                executiveId: formData.executiveId || null,\n                contactPersonId: formData.contactPersonId || null,\n                bslNo: formData.bslNo || null,\n                bslDate: formData.bslDate ? new Date(formData.bslDate).toISOString() : null,\n                bslAmount: formData.bslAmount ? parseFloat(formData.bslAmount) : 0,\n                frequency: formData.frequency ? parseInt(formData.frequency) : 0,\n                numberOfMachines: parseInt(formData.numberOfMachines),\n                installDate: formData.installDate ? new Date(formData.installDate).toISOString() : null,\n                warrantyDate: formData.warrantyDate ? new Date(formData.warrantyDate).toISOString() : null,\n                warningDate: formData.warningDate ? new Date(formData.warningDate).toISOString() : null,\n                technicianId: formData.technicianId || null,\n                amcId: formData.amcId || null,\n                status: formData.status,\n                // Add BLUESTAR specific data if applicable\n                ...vendor === 'bluestar' && {\n                    vendorSpecific: {\n                        bluestarWarrantyCode: formData.bluestarWarrantyCode,\n                        bluestarServiceCenter: formData.bluestarServiceCenter,\n                        bluestarContactPerson: formData.bluestarContactPerson,\n                        bluestarContactPhone: formData.bluestarContactPhone,\n                        specialTerms: formData.specialTerms\n                    }\n                }\n            };\n            const response = await fetch('/api/warranties', {\n                method: 'POST',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(warrantyData)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to create warranty');\n            }\n            const createdWarranty = await response.json();\n            // Redirect to appropriate page after creation\n            if (warrantyType === 'out-warranty') {\n                window.location.href = '/warranties/out-warranty';\n            } else if (vendor === 'bluestar') {\n                window.location.href = '/warranties/bluestar';\n            } else {\n                window.location.href = '/warranties/in-warranty';\n            }\n        } catch (error) {\n            console.error('Error creating warranty:', error);\n            setErrors({\n                submit: error.message || 'Failed to create warranty. Please try again.'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const getPageTitle = ()=>{\n        if (vendor === 'bluestar') return 'New BLUESTAR Warranty';\n        if (warrantyType === 'out-warranty') return 'New Out-of-Warranty';\n        return 'New In-Warranty';\n    };\n    const getBackUrl = ()=>{\n        if (vendor === 'bluestar') return '/warranties/bluestar';\n        if (warrantyType === 'out-warranty') return '/warranties/out-warranty';\n        return '/warranties/in-warranty';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getPageTitle()\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Create a new warranty record with machine and component details\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"secondary\",\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                    href: getBackUrl(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Cancel\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                                defaultValue: \"basic\",\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"basic\",\n                                                children: \"Basic Information\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"dates\",\n                                                children: \"Dates & Terms\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"machines\",\n                                                children: \"Machines\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            vendor === 'bluestar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"bluestar\",\n                                                children: \"BLUESTAR Details\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"basic\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"customerId\",\n                                                            className: \"text-black\",\n                                                            children: \"Customer *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.customerId,\n                                                            onValueChange: (value)=>handleInputChange('customerId', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"customerId\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: isLoadingData ? \"Loading customers...\" : \"Select customer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: customer.id,\n                                                                                children: [\n                                                                                    customer.name,\n                                                                                    \" \",\n                                                                                    customer.city && \"(\".concat(customer.city, \")\")\n                                                                                ]\n                                                                            }, customer.id, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 234,\n                                                                                columnNumber: 52\n                                                                            }, this)),\n                                                                        customers.length === 0 && !isLoadingData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"no-customers\",\n                                                                            disabled: true,\n                                                                            children: \"No customers available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 237,\n                                                                            columnNumber: 70\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.customerId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.customerId\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"executiveId\",\n                                                            className: \"text-black\",\n                                                            children: \"Executive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.executiveId,\n                                                            onValueChange: (value)=>handleInputChange('executiveId', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"executiveId\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: isLoadingData ? \"Loading executives...\" : \"Select executive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        executives.map((executive)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: executive.id,\n                                                                                children: [\n                                                                                    executive.name,\n                                                                                    \" \",\n                                                                                    executive.email && \"(\".concat(executive.email, \")\")\n                                                                                ]\n                                                                            }, executive.id, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 252,\n                                                                                columnNumber: 54\n                                                                            }, this)),\n                                                                        executives.length === 0 && !isLoadingData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"no-executives\",\n                                                                            disabled: true,\n                                                                            children: \"No executives available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 71\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslNo\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Number *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bslNo\",\n                                                            value: formData.bslNo,\n                                                            onChange: (e)=>handleInputChange('bslNo', e.target.value),\n                                                            placeholder: \"Enter BSL number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.bslNo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.bslNo\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 38\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslAmount\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"bslAmount\",\n                                                                    type: \"number\",\n                                                                    value: formData.bslAmount,\n                                                                    onChange: (e)=>handleInputChange('bslAmount', e.target.value),\n                                                                    placeholder: \"0\",\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"numberOfMachines\",\n                                                            className: \"text-black\",\n                                                            children: \"Number of Machines *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"numberOfMachines\",\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            value: formData.numberOfMachines,\n                                                            onChange: (e)=>handleInputChange('numberOfMachines', e.target.value),\n                                                            placeholder: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.numberOfMachines && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.numberOfMachines\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            className: \"text-black\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.status,\n                                                            onValueChange: (value)=>handleInputChange('status', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"status\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"ACTIVE\",\n                                                                            children: \"Active\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"PENDING\",\n                                                                            children: \"Pending\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"EXPIRED\",\n                                                                            children: \"Expired\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"CANCELLED\",\n                                                                            children: \"Cancelled\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"dates\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bslDate\",\n                                                            className: \"text-black\",\n                                                            children: \"BSL Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"bslDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.bslDate,\n                                                                    onChange: (e)=>handleInputChange('bslDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"installDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Install Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"installDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.installDate,\n                                                                    onChange: (e)=>handleInputChange('installDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"warrantyDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Warranty Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"warrantyDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.warrantyDate,\n                                                                    onChange: (e)=>handleInputChange('warrantyDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        errors.warrantyDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.warrantyDate\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"warningDate\",\n                                                            className: \"text-black\",\n                                                            children: \"Warning Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"warningDate\",\n                                                                    type: \"date\",\n                                                                    value: formData.warningDate,\n                                                                    onChange: (e)=>handleInputChange('warningDate', e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"frequency\",\n                                                            className: \"text-black\",\n                                                            children: \"Frequency\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"frequency\",\n                                                            type: \"number\",\n                                                            value: formData.frequency,\n                                                            onChange: (e)=>handleInputChange('frequency', e.target.value),\n                                                            placeholder: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"machines\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-black mb-2\",\n                                                    children: \"Machine Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Machine and component assignment interface will be implemented here.\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    type: \"button\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Add Machine\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    vendor === 'bluestar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"bluestar\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarWarrantyCode\",\n                                                            className: \"text-black\",\n                                                            children: \"BLUESTAR Warranty Code *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarWarrantyCode\",\n                                                            value: formData.bluestarWarrantyCode,\n                                                            onChange: (e)=>handleInputChange('bluestarWarrantyCode', e.target.value),\n                                                            placeholder: \"Enter BLUESTAR warranty code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.bluestarWarrantyCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: errors.bluestarWarrantyCode\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarServiceCenter\",\n                                                            className: \"text-black\",\n                                                            children: \"Service Center\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.bluestarServiceCenter,\n                                                            onValueChange: (value)=>handleInputChange('bluestarServiceCenter', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    id: \"bluestarServiceCenter\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select service center\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Mumbai Central Service Center\",\n                                                                            children: \"Mumbai Central\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: \"Delhi North Service Center\",\n                                                                            children: \"Delhi North\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarContactPerson\",\n                                                            className: \"text-black\",\n                                                            children: \"Contact Person\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarContactPerson\",\n                                                            value: formData.bluestarContactPerson,\n                                                            onChange: (e)=>handleInputChange('bluestarContactPerson', e.target.value),\n                                                            placeholder: \"Enter contact person name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"bluestarContactPhone\",\n                                                            className: \"text-black\",\n                                                            children: \"Contact Phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"bluestarContactPhone\",\n                                                            value: formData.bluestarContactPhone,\n                                                            onChange: (e)=>handleInputChange('bluestarContactPhone', e.target.value),\n                                                            placeholder: \"Enter contact phone number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"specialTerms\",\n                                                            className: \"text-black\",\n                                                            children: \"Special Terms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_8__.Textarea, {\n                                                            id: \"specialTerms\",\n                                                            value: formData.specialTerms,\n                                                            onChange: (e)=>handleInputChange('specialTerms', e.target.value),\n                                                            placeholder: \"Enter any special terms or conditions\",\n                                                            rows: 3\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                        className: \"text-black\",\n                                        children: errors.submit\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4 mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                            href: getBackUrl(),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Creating...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_DollarSign_Plus_Save_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create Warranty\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\new\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 10\n    }, this);\n}\n_s(NewWarrantyPage, \"oEA5x5Ns+OO7pzg4GKVLkkF1wtI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = NewWarrantyPage;\n_s1(NewWarrantyPage, \"oEA5x5Ns+OO7pzg4GKVLkkF1wtI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = NewWarrantyPage;\nvar _c;\n$RefreshReg$(_c, \"NewWarrantyPage\");\nvar _c1;\n$RefreshReg$(_c1, \"NewWarrantyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/new/page.tsx\n"));

/***/ })

});