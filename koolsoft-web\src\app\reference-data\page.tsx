'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import {
  Home,
  Database,
  RefreshCw,
  ChevronRight,
  Tag,
  MapPin,
  Users,
  FileText,
  AlertTriangle,
  Wrench,
  Ruler,
  Layers,
  Bookmark
} from 'lucide-react';

// Define reference data categories with icons and descriptions
const referenceCategories = [
  {
    id: 'territories',
    name: 'Territories',
    description: 'Geographic regions for business operations',
    icon: <MapPin className="h-6 w-6" />,
    color: 'bg-blue-100 text-blue-600'
  },
  {
    id: 'segments',
    name: 'Segments',
    description: 'Customer segmentation categories',
    icon: <Users className="h-6 w-6" />,
    color: 'bg-green-100 text-green-600'
  },
  {
    id: 'competitors',
    name: 'Competitors',
    description: 'Competing businesses in the market',
    icon: <Tag className="h-6 w-6" />,
    color: 'bg-purple-100 text-purple-600'
  },
  {
    id: 'serviceVisitType',
    name: 'Service Visit Types',
    description: 'Types of service visits conducted',
    icon: <Wrench className="h-6 w-6" />,
    color: 'bg-orange-100 text-orange-600'
  },
  {
    id: 'complaintType',
    name: 'Complaint Types',
    description: 'Categories of customer complaints',
    icon: <AlertTriangle className="h-6 w-6" />,
    color: 'bg-red-100 text-red-600'
  },
  {
    id: 'complaintNatureType',
    name: 'Complaint Nature Types',
    description: 'Nature or characteristics of complaints',
    icon: <FileText className="h-6 w-6" />,
    color: 'bg-yellow-100 text-yellow-600'
  },
  {
    id: 'failureType',
    name: 'Failure Types',
    description: 'Categories of equipment failures',
    icon: <Wrench className="h-6 w-6" />,
    color: 'bg-indigo-100 text-indigo-600'
  },
  {
    id: 'spareType',
    name: 'Spare Types',
    description: 'Categories of spare parts',
    icon: <Wrench className="h-6 w-6" />,
    color: 'bg-teal-100 text-teal-600'
  },
  {
    id: 'measurementType',
    name: 'Measurement Types',
    description: 'Types of measurements used',
    icon: <Ruler className="h-6 w-6" />,
    color: 'bg-pink-100 text-pink-600'
  },
  {
    id: 'priorityTypes',
    name: 'Priority Types',
    description: 'Levels of priority for tasks and issues',
    icon: <AlertTriangle className="h-6 w-6" />,
    color: 'bg-red-100 text-red-600'
  },
  {
    id: 'enquiryTypes',
    name: 'Enquiry Types',
    description: 'Categories of customer enquiries',
    icon: <FileText className="h-6 w-6" />,
    color: 'bg-yellow-100 text-yellow-600'
  },
  {
    id: 'deductionTypes',
    name: 'Deduction Types',
    description: 'Types of deductions applied',
    icon: <Tag className="h-6 w-6" />,
    color: 'bg-purple-100 text-purple-600'
  },
  {
    id: 'debitDivisions',
    name: 'Debit Divisions',
    description: 'Divisions for debit categorization',
    icon: <Layers className="h-6 w-6" />,
    color: 'bg-gray-100 text-gray-600'
  },
  {
    id: 'accountDivisions',
    name: 'Account Divisions',
    description: 'Divisions for account categorization',
    icon: <Layers className="h-6 w-6" />,
    color: 'bg-blue-100 text-blue-600'
  },
  {
    id: 'spareParts',
    name: 'Spare Parts',
    description: 'Replacement parts for equipment',
    icon: <Wrench className="h-6 w-6" />,
    color: 'bg-teal-100 text-teal-600'
  },
  {
    id: 'taxRates',
    name: 'Tax Rates',
    description: 'Different tax rates applied to products',
    icon: <Tag className="h-6 w-6" />,
    color: 'bg-green-100 text-green-600'
  },
  {
    id: 'divisions',
    name: 'Divisions',
    description: 'Business divisions or departments',
    icon: <Layers className="h-6 w-6" />,
    color: 'bg-gray-100 text-gray-600'
  },
  {
    id: 'brands',
    name: 'Brands',
    description: 'Product brands and manufacturers',
    icon: <Bookmark className="h-6 w-6" />,
    color: 'bg-blue-100 text-blue-600'
  }
];

/**
 * Reference Data Management Page
 *
 * This page displays all reference data categories and allows users to navigate to specific category pages.
 */
export default function ReferenceDataPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categoryCounts, setCategoryCounts] = useState<Record<string, number>>({});

  // Fetch category counts
  useEffect(() => {
    const fetchCategoryCounts = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const counts: Record<string, number> = {};

        // Fetch counts for each category
        for (const category of referenceCategories) {
          try {
            const response = await fetch(`/api/reference/${category.id}?take=1`, {
              credentials: 'include'
            });

            if (response.ok) {
              const data = await response.json();
              counts[category.id] = data.meta?.total || 0;
            }
          } catch (categoryError) {
            console.error(`Error fetching count for ${category.name}:`, categoryError);
          }
        }

        setCategoryCounts(counts);
      } catch (error: any) {
        console.error('Error fetching reference data counts:', error);
        setError(error.message || 'Failed to load reference data counts');
        toast({
          title: 'Error',
          description: 'Failed to load reference data counts. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategoryCounts();
  }, []);

  // Handle refresh
  const handleRefresh = () => {
    setIsLoading(true);
    setError(null);

    const fetchCategoryCounts = async () => {
      try {
        const counts: Record<string, number> = {};

        // Fetch counts for each category
        for (const category of referenceCategories) {
          try {
            const response = await fetch(`/api/reference/${category.id}?take=1`, {
              credentials: 'include'
            });

            if (response.ok) {
              const data = await response.json();
              counts[category.id] = data.meta?.total || 0;
            }
          } catch (categoryError) {
            console.error(`Error fetching count for ${category.name}:`, categoryError);
          }
        }

        setCategoryCounts(counts);
        toast({
          title: 'Success',
          description: 'Reference data counts refreshed successfully'
        });
      } catch (error: any) {
        console.error('Error refreshing reference data counts:', error);
        setError(error.message || 'Failed to refresh reference data counts');
        toast({
          title: 'Error',
          description: 'Failed to refresh reference data counts. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategoryCounts();
  };

  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="pb-3 bg-primary text-white">
          <div className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Reference Data Management</CardTitle>
              <CardDescription className="text-gray-100">
                Manage reference data categories used throughout the application
              </CardDescription>
            </div>
            <Button variant="secondary" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          {error ? (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
              <strong className="font-bold">Error: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {referenceCategories.map((category) => (
                <Card key={category.id} className="overflow-hidden">
                  <CardHeader className={`flex flex-row items-center gap-3 ${category.color} p-4`}>
                    <div className="rounded-full bg-white p-2">
                      {category.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{category.name}</CardTitle>
                      <CardDescription className="text-gray-700">
                        {category.description}
                      </CardDescription>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Database className="h-4 w-4 mr-2 text-gray-500" />
                        <span>
                          {isLoading ? (
                            <Skeleton className="h-4 w-16" />
                          ) : (
                            `${categoryCounts[category.id] || 0} items`
                          )}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-end">
                    <Button asChild variant="default">
                      <Link href={`/reference-data/${category.id}`}>
                        Manage
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
